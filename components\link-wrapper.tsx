'use client';

import Link from 'next/link';
import { forwardRef } from 'react';
import type { ComponentProps } from 'react';

/**
 * A wrapper around Next.js Link that suppresses hydration warnings
 * caused by browser extensions adding attributes like rtrvr-listeners
 */
export const LinkWrapper = forwardRef<
  HTMLAnchorElement,
  ComponentProps<typeof Link>
>(({ children, ...props }, ref) => {
  return (
    <Link ref={ref} {...props} suppressHydrationWarning>
      {children}
    </Link>
  );
});

LinkWrapper.displayName = 'LinkWrapper';
