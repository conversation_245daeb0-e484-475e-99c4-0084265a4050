# Mastra Agent Integration

This document outlines the comprehensive integration of Mastra agents into your existing chat interface, significantly enhancing its capabilities with specialized AI agents, persistent memory, advanced tools, and complex workflows.

## 🚀 What's New

### Specialized AI Agents
- **General Chat Assistant**: Conversational AI for general questions and discussions
- **Code Assistant**: Expert software engineer for coding, debugging, and architecture
- **Research Assistant**: <PERSON>ough researcher with fact-checking and source verification
- **Document Analyst**: Specialist in analyzing and extracting insights from documents

### Advanced Features
- **Persistent Memory**: Agents remember context across conversations
- **Dynamic Configuration**: Agents adapt based on user preferences and context
- **Tool Integration**: Web search, code execution, file analysis capabilities
- **Complex Workflows**: Multi-step automation for research and code review
- **Real-time Streaming**: Enhanced streaming with tool usage indicators

## 📁 Project Structure

```
mastra/
├── index.ts                    # Main Mastra configuration
├── agents/                     # Specialized AI agents
│   ├── general-chat-agent.ts
│   ├── code-assistant-agent.ts
│   ├── research-agent.ts
│   └── document-analysis-agent.ts
├── tools/                      # Custom tools for agents
│   ├── web-search-tool.ts
│   ├── code-execution-tool.ts
│   └── file-analysis-tool.ts
└── workflows/                  # Complex multi-step workflows
    ├── research-workflow.ts
    └── code-review-workflow.ts

components/
├── enhanced-chat.tsx           # Enhanced chat with agent features
├── agent-selector.tsx          # Agent selection interface
├── memory-panel.tsx            # Memory visualization
├── workflow-panel.tsx          # Workflow execution interface
└── ui/                         # Additional UI components

app/(chat)/api/
├── mastra-chat/route.ts        # Enhanced chat API with agents
└── workflows/execute/route.ts  # Workflow execution API
```

## 🛠️ Setup Instructions

### 1. Environment Variables

Add these to your `.env` file:

```env
# Existing variables
POSTGRES_URL=your_postgres_connection_string
OPENAI_API_KEY=your_openai_api_key

# Optional: For enhanced web search (replace mock implementation)
GOOGLE_SEARCH_API_KEY=your_google_search_api_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
```

### 2. Database Setup

The integration uses your existing PostgreSQL database. Mastra will automatically create the necessary tables for memory storage.

### 3. Usage

#### Using the Enhanced Chat Interface

Replace your existing chat component with the enhanced version:

```tsx
import { EnhancedChat } from '@/components/enhanced-chat';

// In your chat page
<EnhancedChat
  id={chatId}
  initialMessages={messages}
  initialChatModel={model}
  initialVisibilityType={visibility}
  isReadonly={false}
  session={session}
  autoResume={true}
/>
```

#### Agent Selection

Users can now select different agents based on their needs:
- **General Assistant**: For everyday conversations
- **Code Assistant**: For programming help
- **Research Assistant**: For in-depth research (Premium)
- **Document Analyst**: For document analysis (Premium)

#### Memory Features

- Agents remember user preferences, facts, and context
- Memory is visualized in the Memory Panel
- Persistent across chat sessions
- Categorized by type (preferences, facts, context, conversations)

#### Workflow Automation

Execute complex multi-step workflows:
- **Research Workflow**: Comprehensive research with fact-checking
- **Code Review Workflow**: Security, performance, and quality analysis

## 🔧 Customization

### Adding New Agents

1. Create a new agent file in `mastra/agents/`:

```typescript
import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';

export const myCustomAgent = new Agent({
  name: 'My Custom Agent',
  instructions: 'Your agent instructions...',
  model: openai('gpt-4o'),
  tools: ['webSearch', 'fileAnalysis'],
});
```

2. Add to `mastra/index.ts`:

```typescript
import { myCustomAgent } from './agents/my-custom-agent';

export const mastra = new Mastra({
  agents: {
    // ... existing agents
    myCustom: myCustomAgent,
  },
  // ... rest of config
});
```

3. Update the agent selector in `components/agent-selector.tsx`

### Adding New Tools

1. Create a tool file in `mastra/tools/`:

```typescript
import { createTool } from '@mastra/core';
import { z } from 'zod';

export const myCustomTool = createTool({
  id: 'myCustomTool',
  name: 'My Custom Tool',
  description: 'Description of what the tool does',
  inputSchema: z.object({
    input: z.string(),
  }),
  execute: async ({ input }, { runtimeContext }) => {
    // Tool implementation
    return { result: 'Tool output' };
  },
});
```

2. Add to `mastra/index.ts` and agent configurations

### Creating Workflows

1. Create a workflow file in `mastra/workflows/`:

```typescript
import { Workflow, createStep } from '@mastra/core';

export const myWorkflow = new Workflow({
  name: 'My Custom Workflow',
  description: 'Workflow description',
  steps: [
    createStep({
      id: 'step-1',
      name: 'First Step',
      execute: async (input, { tools, agents }) => {
        // Step implementation
        return { stepResult: 'data' };
      },
    }),
    // ... more steps
  ],
});
```

## 🔍 API Endpoints

### Enhanced Chat API
- **POST** `/api/mastra-chat`
- Supports agent selection, memory integration, and tool usage
- Compatible with existing chat interface

### Workflow Execution API
- **POST** `/api/workflows/execute`
- Execute complex multi-step workflows
- Returns structured results

## 🎯 Key Benefits

1. **Specialized Expertise**: Different agents for different tasks
2. **Persistent Context**: Agents remember across sessions
3. **Advanced Capabilities**: Web search, code execution, file analysis
4. **Workflow Automation**: Complex multi-step task execution
5. **Enhanced UX**: Real-time indicators, memory visualization
6. **Scalable Architecture**: Easy to add new agents, tools, and workflows

## 🚦 Next Steps

1. **Test the Integration**: Try different agents and workflows
2. **Customize Agents**: Adjust instructions and capabilities for your use case
3. **Add Real APIs**: Replace mock implementations with real services
4. **Monitor Performance**: Use the built-in observability features
5. **Expand Workflows**: Create domain-specific automation workflows

## 🔒 Security Considerations

- Code execution is sandboxed but should be used carefully
- Consider containerization for production code execution
- Implement proper authentication for premium features
- Monitor and log all agent interactions
- Review and validate workflow outputs

## 📊 Performance Tips

- Use appropriate models for different agents (GPT-4o for complex tasks, GPT-4o-mini for simple ones)
- Implement caching for frequently used tools
- Monitor token usage and costs
- Use streaming for better user experience
- Optimize memory queries for large conversation histories

This integration transforms your chat interface into a powerful AI-powered workspace with specialized agents, persistent memory, and advanced automation capabilities.
