import { <PERSON><PERSON> } from '@mastra/core';

// Import agents
import { codeAssistantAgent } from './agents/code-assistant-agent';
import { documentAnalysisAgent } from './agents/document-analysis-agent';
import { generalChatAgent } from './agents/general-chat-agent';
import { researchAgent } from './agents/research-agent';

// Import tools
import { codeExecutionTool } from './tools/code-execution-tool';
import { fileAnalysisTool } from './tools/file-analysis-tool';
import { webSearchTool } from './tools/web-search-tool';

// Import workflows
import { codeReviewWorkflow } from './workflows/code-review-workflow';
import { researchWorkflow } from './workflows/research-workflow';

// Create the main Mastra instance
export const mastra = new Mastra({
  agents: {
    generalChat: generalChatAgent,
    codeAssistant: codeAssistantAgent,
    research: researchAgent,
    documentAnalysis: documentAnalysisAgent,
  },
  tools: [
    webSearchTool,
    codeExecutionTool,
    fileAnalysisTool,
    ragSearchTool,
    ragAddTool,
    ragContextTool,
    ragHealthTool,
  ],
  workflows: [researchWorkflow, codeReviewWorkflow],
  server: {
    port: 4111,
    middleware: [
      // Add custom middleware for authentication, logging, etc.
      async (c, next) => {
        const runtimeContext = c.get('runtimeContext');

        // Extract user information from request headers or session
        const userId = c.req.header('x-user-id');
        if (userId) {
          runtimeContext.set('userId', userId);
        }

        await next();
      },
    ],
  },
});

// Export individual agents for direct use
export {
  codeAssistantAgent,
  documentAnalysisAgent,
  generalChatAgent,
  researchAgent,
};
