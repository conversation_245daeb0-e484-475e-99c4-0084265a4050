import { NextRequest } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { chromaRAGService } from '@/lib/rag/chroma-service';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { chatId, userId, limit = 20 } = await request.json();

    if (!chatId || !userId) {
      return new Response('Missing required fields', { status: 400 });
    }

    // Verify user can access this chat
    if (userId !== session.user.id) {
      return new Response('Forbidden', { status: 403 });
    }

    const [context, stats] = await Promise.all([
      chromaRAGService.getConversationContext(userId, chatId, limit),
      chromaRAGService.getCollectionStats(userId, chatId),
    ]);

    return Response.json({
      success: true,
      context,
      chatStats: stats,
      totalItems: context.length,
    });

  } catch (error) {
    console.error('RAG context error:', error);
    
    return Response.json(
      {
        success: false,
        error: 'Failed to get RAG context',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
