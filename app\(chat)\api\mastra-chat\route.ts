import { auth } from '@/app/(auth)/auth';
import {
  createStreamId,
  getChatById,
  getMessagesByChatId,
  saveChat,
  saveMessages,
} from '@/lib/db/queries';
import { convertToUIMessages, generateUUID } from '@/lib/utils';
import { mastra } from '@/mastra';
import { RuntimeContext } from '@mastra/core';
import { NextRequest } from 'next/server';
import { generateTitleFromUserMessage } from '../../actions';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const {
      id: chatId,
      message,
      selectedAgent = 'generalChat',
      selectedChatModel,
      selectedVisibilityType,
      userPreferences,
      projectContext,
    } = await request.json();

    // Create runtime context with user and session information
    const runtimeContext = new RuntimeContext();
    runtimeContext.set('userId', session.user.id);
    runtimeContext.set('userEmail', session.user.email);
    runtimeContext.set('chatId', chatId);
    runtimeContext.set('selectedModel', selectedChatModel);
    runtimeContext.set('visibilityType', selectedVisibilityType);

    // Add user preferences for dynamic agent configuration
    if (userPreferences) {
      runtimeContext.set('userPreferences', userPreferences);
    }

    // Add project context for code assistant
    if (projectContext) {
      runtimeContext.set('projectType', projectContext.type);
      runtimeContext.set('programmingLanguage', projectContext.language);
    }

    // Get or create chat
    let chat = await getChatById({ id: chatId });

    if (!chat) {
      // Create new chat
      const title = await generateTitleFromUserMessage({
        message: message.content,
      });
      chat = await saveChat({
        id: chatId,
        userId: session.user.id,
        title,
        visibility: selectedVisibilityType,
      });
    }

    // Get existing messages
    const existingMessages = await getMessagesByChatId({ id: chatId });
    const uiMessages = convertToUIMessages(existingMessages);

    // Add the new user message
    const userMessage = {
      id: generateUUID(),
      role: 'user' as const,
      content: message.content,
      createdAt: new Date(),
    };

    // Save the user message
    await saveMessages({
      messages: [
        {
          id: userMessage.id,
          chatId,
          role: userMessage.role,
          content: userMessage.content,
          createdAt: userMessage.createdAt,
        },
      ],
    });

    // Enhance user message with RAG context
    const { enhancedMessage, contextUsed, contextSummary } =
      await ragMiddleware.enhanceMessageWithContext(
        userMessage,
        session.user.id,
        chatId,
      );

    // Prepare messages for the agent (use enhanced message for user input)
    const allMessages = [...uiMessages, enhancedMessage];

    // Get the selected agent
    const agent = mastra.getAgent(selectedAgent);

    if (!agent) {
      return new Response(`Agent "${selectedAgent}" not found`, {
        status: 400,
      });
    }

    // Log RAG context usage
    if (contextUsed.length > 0) {
      console.log(
        `RAG Context: ${contextSummary} - Used ${contextUsed.length} items`,
      );
    }

    // Create a stream ID for tracking
    const streamId = createStreamId();

    // Stream response from the agent
    const stream = await agent.stream(allMessages, {
      runtimeContext,
      maxSteps: 5,
      onStepFinish: ({ text, toolCalls, toolResults }) => {
        // Log tool usage for analytics
        if (toolCalls?.length) {
          console.log(
            `Agent ${selectedAgent} used tools:`,
            toolCalls.map((tc) => tc.toolName),
          );
        }
      },
    });

    // Convert to AI SDK compatible response
    const response = stream.toDataStreamResponse({
      onFinish: async ({ text, finishReason, usage }) => {
        // Save the assistant's response
        const assistantMessage = {
          id: generateUUID(),
          chatId,
          role: 'assistant' as const,
          content: text,
          createdAt: new Date(),
        };

        await saveMessages({
          messages: [assistantMessage],
        });

        // Store both user and assistant messages in RAG
        await Promise.all([
          ragMiddleware.storeMessage(
            userMessage,
            session.user.id,
            chatId,
            userMessage.id,
          ),
          ragMiddleware.storeMessage(
            assistantMessage,
            session.user.id,
            chatId,
            assistantMessage.id,
          ),
        ]);

        // Log usage statistics
        console.log(`Agent ${selectedAgent} completed:`, {
          finishReason,
          usage,
          streamId,
          ragContextUsed: contextUsed.length,
        });
      },
    });

    return response;
  } catch (error) {
    console.error('Mastra chat error:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
}
