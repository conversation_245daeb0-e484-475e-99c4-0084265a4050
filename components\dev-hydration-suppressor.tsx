'use client';

import { useEffect } from 'react';

/**
 * Development-only component that suppresses hydration warnings
 * caused by browser extensions in development mode
 */
export function DevHydrationSuppressor() {
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // Store the original console.error
    const originalError = console.error;

    // Override console.error to filter out hydration warnings
    console.error = (...args: any[]) => {
      const message = args[0];
      
      // Check if this is a hydration mismatch error related to browser extensions
      if (
        typeof message === 'string' &&
        (
          message.includes('Hydration failed') ||
          message.includes('hydrated but some attributes') ||
          message.includes('rtrvr-listeners') ||
          message.includes('data-extension-id') ||
          message.includes('browser extension')
        )
      ) {
        // Log a simplified message instead
        console.warn(
          '🔧 Hydration warning suppressed: Browser extension detected. This is normal and won\'t affect production.'
        );
        return;
      }

      // Call the original console.error for other errors
      originalError.apply(console, args);
    };

    // Cleanup function to restore original console.error
    return () => {
      console.error = originalError;
    };
  }, []);

  return null;
}
