# 🚀 Setup Guide - <PERSON><PERSON> Enhanced Chat

This guide will help you set up your enhanced chat interface with Mastra agents.

## ✅ Authentication Issue Fixed

The `MissingSecret` error has been resolved by:
1. ✅ Adding `AUTH_SECRET` to `.env.local`
2. ✅ Configuring NextAuth.js properly
3. ✅ Generated secure authentication secret

## 🔧 Environment Setup

### 1. Environment Variables

Your `.env.local` file has been created with the following structure:

```env
# NextAuth.js Secret - Required for authentication
AUTH_SECRET=FhCvtCaS17+O/TE0bEo70u9fZM2QvKrM+TjnOWBL+UM=

# Database Configuration (REQUIRED)
POSTGRES_URL=postgresql://username:password@localhost:5432/database_name

# AI API Keys (REQUIRED)
OPENAI_API_KEY=your-openai-api-key-here

# Optional: Additional features
XAI_API_KEY=your-xai-api-key-here
BLOB_READ_WRITE_TOKEN=your-blob-token-here
REDIS_URL=your-redis-url-here
GOOGLE_SEARCH_API_KEY=your-google-search-api-key
GOOGLE_SEARCH_ENGINE_ID=your-search-engine-id

# ChromaDB Configuration for RAG (Vector Database) - CONFIGURED
CHROMA_API_KEY=ck-5X831whg7iha9NaDnPFGxF2KyGQLVttGeHCjsQFi3ZDi
CHROMA_TENANT=ae085e38-67c9-4d61-a73b-328ddbe3ee4c
CHROMA_DATABASE=vovagpt
```

### 2. Required Configuration

**You MUST update these values:**

1. **POSTGRES_URL**: Your PostgreSQL database connection string
2. **OPENAI_API_KEY**: Your OpenAI API key from https://platform.openai.com/api-keys

### 3. Optional Configuration

- **XAI_API_KEY**: For additional AI models
- **GOOGLE_SEARCH_API_KEY**: For enhanced web search capabilities
- **BLOB_READ_WRITE_TOKEN**: For file upload features
- **REDIS_URL**: For caching (improves performance)
- **ChromaDB**: ✅ Already configured for RAG (vector database)

## 🗄️ Database Setup

### Option 1: Local PostgreSQL
```bash
# Install PostgreSQL locally
# Create a database
createdb your_database_name

# Update POSTGRES_URL in .env.local
POSTGRES_URL=postgresql://username:password@localhost:5432/your_database_name
```

### Option 2: Vercel Postgres (Recommended)
1. Go to https://vercel.com/dashboard
2. Create a new project or select existing
3. Go to Storage → Create Database → Postgres
4. Copy the connection string to your `.env.local`

### Option 3: Other PostgreSQL Providers
- **Supabase**: https://supabase.com/
- **Railway**: https://railway.app/
- **Neon**: https://neon.tech/
- **PlanetScale**: https://planetscale.com/

## 🚀 Getting Started

### 1. Install Dependencies
```bash
pnpm install
```

### 2. Set Up Database
```bash
# Generate and run database migrations
pnpm db:generate
pnpm db:migrate
```

### 3. Test the Integration
```bash
# Run the Mastra integration test
npx tsx test-mastra-integration.ts
```

### 4. Start Development Server
```bash
pnpm dev
```

### 5. Access Your Enhanced Chat
1. Open http://localhost:3000
2. Sign in or create an account
3. Start a new chat
4. Try selecting different agents
5. Explore memory and workflow panels

## 🎯 Features to Test

### Agent Selection
- **General Assistant**: Ask general questions
- **Code Assistant**: Request code help, debugging, or reviews
- **Research Assistant**: Ask for research on any topic
- **Document Analyst**: Upload and analyze documents

### Memory System
- Click the "Memory" button to see what agents remember
- Have conversations and watch memory build up
- Memory persists across chat sessions

### Workflow Automation
- Click "Workflows" to access complex automations
- Try the "Comprehensive Research" workflow
- Try the "Code Review" workflow

### Tool Usage
- Watch for tool usage indicators during conversations
- Agents can search the web, execute code, and analyze files

### RAG (Retrieval-Augmented Generation)
- **Vector Database**: ChromaDB stores conversation history for semantic search
- **Per-Chat Collections**: Each chat session has its own vector collection
- **Automatic Storage**: Messages are automatically stored and indexed
- **Semantic Search**: Find relevant context from conversation history
- **RAG Panel**: Click "RAG" button to search and explore stored conversations
- **Enhanced Context**: Agents automatically use relevant context for better responses

## 🔍 Troubleshooting

### Common Issues

**1. Database Connection Error**
```
Error: Connection refused
```
**Solution**: Check your `POSTGRES_URL` in `.env.local`

**2. OpenAI API Error**
```
Error: Invalid API key
```
**Solution**: Verify your `OPENAI_API_KEY` in `.env.local`

**3. Authentication Error**
```
Error: MissingSecret
```
**Solution**: ✅ Already fixed! The `AUTH_SECRET` is properly configured.

**4. Module Not Found**
```
Error: Cannot find module '@mastra/core'
```
**Solution**: Run `pnpm install` to install all dependencies

### Getting Help

1. Check the console for detailed error messages
2. Review the `MASTRA_INTEGRATION.md` file
3. Ensure all required environment variables are set
4. Verify your database is accessible

## 📚 Next Steps

1. **Customize Agents**: Modify agent instructions in `mastra/agents/`
2. **Add Tools**: Create custom tools in `mastra/tools/`
3. **Create Workflows**: Build automation in `mastra/workflows/`
4. **Enhance UI**: Customize components in `components/`

## 🔐 Security Notes

- ✅ `AUTH_SECRET` is properly configured
- 🔒 Never commit `.env.local` to version control
- 🛡️ Code execution is sandboxed but use with caution
- 🔍 Monitor agent interactions in production
- 🚨 Implement rate limiting for production use

Your enhanced chat interface with Mastra agents is now ready to use! 🎉
