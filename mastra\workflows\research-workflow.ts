import { Workflow, createStep } from '@mastra/core';
import { z } from 'zod';

// Define the input schema for the research workflow
const researchInputSchema = z.object({
  topic: z.string().describe('The research topic or question'),
  depth: z.enum(['basic', 'detailed', 'comprehensive']).default('detailed'),
  sources: z.array(z.string()).optional().describe('Specific sources to include'),
  excludeSources: z.array(z.string()).optional().describe('Sources to exclude'),
  timeframe: z.string().optional().describe('Time period to focus on (e.g., "last 5 years")'),
});

// Define the output schema
const researchOutputSchema = z.object({
  summary: z.string(),
  keyFindings: z.array(z.string()),
  sources: z.array(z.object({
    title: z.string(),
    url: z.string(),
    credibility: z.number(),
    relevance: z.number(),
  })),
  recommendations: z.array(z.string()),
  gaps: z.array(z.string()).optional(),
});

export const researchWorkflow = new Workflow({
  name: 'Comprehensive Research',
  description: 'Conducts thorough research on any topic with multiple verification steps',
  
  inputSchema: researchInputSchema,
  outputSchema: researchOutputSchema,

  steps: [
    // Step 1: Initial search and source gathering
    createStep({
      id: 'initial-search',
      name: 'Initial Search',
      description: 'Perform initial web searches to gather sources',
      
      execute: async ({ topic, depth, sources, timeframe }, { tools, runtimeContext }) => {
        const searchQueries = [
          topic,
          `${topic} research`,
          `${topic} analysis`,
          `${topic} ${timeframe || 'recent'}`,
        ];

        const searchResults = [];
        
        for (const query of searchQueries) {
          try {
            const result = await tools.webSearch({
              query,
              numResults: depth === 'basic' ? 3 : depth === 'detailed' ? 5 : 8,
              searchType: 'general',
            });
            searchResults.push(...result.results);
          } catch (error) {
            console.error(`Search failed for query: ${query}`, error);
          }
        }

        // Remove duplicates and rank by relevance
        const uniqueResults = searchResults.filter((result, index, self) => 
          index === self.findIndex(r => r.url === result.url)
        );

        return {
          searchResults: uniqueResults,
          totalSources: uniqueResults.length,
        };
      },
    }),

    // Step 2: Source credibility assessment
    createStep({
      id: 'assess-credibility',
      name: 'Assess Source Credibility',
      description: 'Evaluate the credibility and relevance of found sources',
      
      execute: async ({ searchResults }, { agents, runtimeContext }) => {
        const researchAgent = agents.research;
        
        const assessedSources = [];
        
        for (const source of searchResults) {
          try {
            const assessment = await researchAgent.generate([
              {
                role: 'user',
                content: `Assess the credibility and relevance of this source for research:
                
Title: ${source.title}
URL: ${source.url}
Snippet: ${source.snippet}

Rate credibility (0-1) and relevance (0-1) and provide reasoning.`,
              },
            ], {
              output: z.object({
                credibility: z.number().min(0).max(1),
                relevance: z.number().min(0).max(1),
                reasoning: z.string(),
                sourceType: z.enum(['academic', 'news', 'blog', 'official', 'commercial', 'other']),
              }),
            });

            assessedSources.push({
              ...source,
              ...assessment.object,
            });
          } catch (error) {
            console.error(`Failed to assess source: ${source.url}`, error);
            // Add with default scores if assessment fails
            assessedSources.push({
              ...source,
              credibility: 0.5,
              relevance: 0.5,
              reasoning: 'Assessment failed',
              sourceType: 'other' as const,
            });
          }
        }

        // Sort by combined credibility and relevance score
        const rankedSources = assessedSources.sort((a, b) => 
          (b.credibility + b.relevance) - (a.credibility + a.relevance)
        );

        return {
          assessedSources: rankedSources,
          highQualitySources: rankedSources.filter(s => s.credibility > 0.7 && s.relevance > 0.7),
        };
      },
    }),

    // Step 3: Deep analysis and synthesis
    createStep({
      id: 'analyze-synthesize',
      name: 'Analyze and Synthesize',
      description: 'Perform deep analysis and synthesize findings',
      
      execute: async ({ topic, assessedSources, highQualitySources }, { agents, runtimeContext }) => {
        const researchAgent = agents.research;
        
        // Use top sources for analysis
        const topSources = assessedSources.slice(0, 10);
        const sourceContent = topSources.map(s => 
          `Source: ${s.title}\nURL: ${s.url}\nContent: ${s.snippet}\nCredibility: ${s.credibility}\n`
        ).join('\n---\n');

        const analysis = await researchAgent.generate([
          {
            role: 'user',
            content: `Conduct a comprehensive analysis of the following sources about "${topic}":

${sourceContent}

Provide:
1. A comprehensive summary
2. Key findings (5-10 points)
3. Recommendations based on the research
4. Any gaps or limitations in the available information`,
          },
        ], {
          output: z.object({
            summary: z.string(),
            keyFindings: z.array(z.string()),
            recommendations: z.array(z.string()),
            gaps: z.array(z.string()),
            confidence: z.number().min(0).max(1),
          }),
        });

        return {
          analysis: analysis.object,
          sourcesUsed: topSources.length,
        };
      },
    }),

    // Step 4: Fact-checking and verification
    createStep({
      id: 'fact-check',
      name: 'Fact Check',
      description: 'Verify key claims and cross-reference information',
      
      execute: async ({ analysis, topic }, { agents, tools, runtimeContext }) => {
        const researchAgent = agents.research;
        
        // Extract key claims for fact-checking
        const keyClaimsToVerify = analysis.keyFindings.slice(0, 5);
        const verificationResults = [];

        for (const claim of keyClaimsToVerify) {
          try {
            // Search for verification of each claim
            const verificationSearch = await tools.webSearch({
              query: `${claim} fact check verification`,
              numResults: 3,
              searchType: 'general',
            });

            const verification = await researchAgent.generate([
              {
                role: 'user',
                content: `Fact-check this claim: "${claim}"

Based on these verification sources:
${verificationSearch.results.map(r => `- ${r.title}: ${r.snippet}`).join('\n')}

Is this claim accurate, partially accurate, or inaccurate? Provide reasoning.`,
              },
            ], {
              output: z.object({
                claim: z.string(),
                accuracy: z.enum(['accurate', 'partially_accurate', 'inaccurate', 'unverifiable']),
                confidence: z.number().min(0).max(1),
                reasoning: z.string(),
              }),
            });

            verificationResults.push(verification.object);
          } catch (error) {
            console.error(`Failed to verify claim: ${claim}`, error);
            verificationResults.push({
              claim,
              accuracy: 'unverifiable' as const,
              confidence: 0,
              reasoning: 'Verification failed',
            });
          }
        }

        return {
          verificationResults,
          overallAccuracy: verificationResults.filter(v => v.accuracy === 'accurate').length / verificationResults.length,
        };
      },
    }),

    // Step 5: Final report generation
    createStep({
      id: 'generate-report',
      name: 'Generate Final Report',
      description: 'Compile all findings into a comprehensive report',
      
      execute: async ({ topic, analysis, assessedSources, verificationResults }, { runtimeContext }) => {
        // Filter sources by quality for final report
        const finalSources = assessedSources
          .filter(s => s.credibility > 0.6 && s.relevance > 0.6)
          .slice(0, 15)
          .map(s => ({
            title: s.title,
            url: s.url,
            credibility: s.credibility,
            relevance: s.relevance,
          }));

        // Adjust findings based on fact-checking
        const verifiedFindings = analysis.keyFindings.map(finding => {
          const verification = verificationResults.find(v => v.claim === finding);
          if (verification && verification.accuracy === 'inaccurate') {
            return `[DISPUTED] ${finding} - ${verification.reasoning}`;
          }
          return finding;
        });

        return {
          summary: analysis.summary,
          keyFindings: verifiedFindings,
          sources: finalSources,
          recommendations: analysis.recommendations,
          gaps: analysis.gaps,
          metadata: {
            totalSourcesEvaluated: assessedSources.length,
            highQualitySourcesUsed: finalSources.length,
            factCheckingAccuracy: verificationResults.filter(v => v.accuracy === 'accurate').length / verificationResults.length,
            researchDepth: 'comprehensive',
            completedAt: new Date().toISOString(),
          },
        };
      },
    }),
  ],
});
