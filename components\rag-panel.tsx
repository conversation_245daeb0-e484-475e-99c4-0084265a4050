'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  Ta<PERSON>,
  <PERSON>bs<PERSON>ontent,
  <PERSON>bsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Database, 
  Search, 
  RefreshCw, 
  Trash2,
  MessageSquare,
  FileText,
  Brain,
  Zap,
  TrendingUp,
} from 'lucide-react';

interface RAGDocument {
  id: string;
  content: string;
  relevanceScore?: number;
  metadata: {
    userId: string;
    chatId: string;
    messageId?: string;
    timestamp: number;
    type: 'message' | 'context' | 'knowledge';
    source?: string;
    title?: string;
  };
}

interface RAGStats {
  documentCount: number;
  collectionName: string;
  userId: string;
  chatId: string;
}

interface RAGPanelProps {
  chatId: string;
  userId: string;
}

export function RAGPanel({ chatId, userId }: RAGPanelProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<RAGDocument[]>([]);
  const [recentDocuments, setRecentDocuments] = useState<RAGDocument[]>([]);
  const [stats, setStats] = useState<RAGStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('search');

  // Load initial data
  useEffect(() => {
    loadRAGData();
  }, [chatId, userId]);

  const loadRAGData = async () => {
    setLoading(true);
    try {
      // Get recent context and stats
      const response = await fetch('/api/rag/context', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ chatId, userId, limit: 20 }),
      });

      if (response.ok) {
        const data = await response.json();
        setRecentDocuments(data.context || []);
        setStats(data.chatStats || null);
      }
    } catch (error) {
      console.error('Error loading RAG data:', error);
    } finally {
      setLoading(false);
    }
  };

  const performSearch = async () => {
    if (!searchQuery.trim()) return;

    setLoading(true);
    try {
      const response = await fetch('/api/rag/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: searchQuery,
          chatId,
          userId,
          limit: 10,
          minRelevanceScore: 0.3,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.results || []);
        setActiveTab('search');
      }
    } catch (error) {
      console.error('Error performing RAG search:', error);
    } finally {
      setLoading(false);
    }
  };

  const clearChatRAG = async () => {
    if (!confirm('Are you sure you want to clear all RAG data for this chat? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch('/api/rag/clear', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ chatId, userId }),
      });

      if (response.ok) {
        setRecentDocuments([]);
        setSearchResults([]);
        setStats(null);
        await loadRAGData();
      }
    } catch (error) {
      console.error('Error clearing RAG data:', error);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'message':
        return <MessageSquare className="w-4 h-4" />;
      case 'context':
        return <Brain className="w-4 h-4" />;
      case 'knowledge':
        return <FileText className="w-4 h-4" />;
      default:
        return <Database className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'message':
        return 'bg-blue-500';
      case 'context':
        return 'bg-purple-500';
      case 'knowledge':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diffInMinutes = Math.floor((now - timestamp) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          <Database className="w-4 h-4" />
          RAG
          {stats && stats.documentCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {stats.documentCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[500px] sm:w-[600px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            RAG Vector Database
          </SheetTitle>
          <SheetDescription>
            Search and explore conversation context stored in the vector database.
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-4">
          {/* Search Interface */}
          <div className="space-y-3">
            <div className="flex gap-2">
              <Input
                placeholder="Search conversation history..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && performSearch()}
                className="flex-1"
              />
              <Button
                onClick={performSearch}
                disabled={loading || !searchQuery.trim()}
                size="sm"
                className="gap-2"
              >
                <Search className="w-4 h-4" />
                Search
              </Button>
            </div>
          </div>

          {/* Stats */}
          {stats && (
            <div className="flex items-center justify-between p-3 border rounded-lg bg-muted/50">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-muted-foreground" />
                <span className="text-sm font-medium">
                  {stats.documentCount} documents stored
                </span>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadRAGData}
                  disabled={loading}
                  className="gap-2"
                >
                  <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearChatRAG}
                  className="gap-2 text-destructive hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4" />
                  Clear
                </Button>
              </div>
            </div>
          )}

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="search">
                Search Results ({searchResults.length})
              </TabsTrigger>
              <TabsTrigger value="recent">
                Recent Context ({recentDocuments.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="search" className="mt-4">
              <ScrollArea className="h-[500px] pr-4">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="w-6 h-6 animate-spin" />
                  </div>
                ) : searchResults.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No search results</p>
                    <p className="text-sm">Try searching for something from your conversation</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {searchResults.map((doc) => (
                      <div
                        key={doc.id}
                        className="p-4 border rounded-lg space-y-3 hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2">
                            <div className={`w-6 h-6 rounded-full ${getTypeColor(doc.metadata.type)} flex items-center justify-center text-white`}>
                              {getTypeIcon(doc.metadata.type)}
                            </div>
                            <Badge variant="outline" className="text-xs capitalize">
                              {doc.metadata.type}
                            </Badge>
                            {doc.relevanceScore && (
                              <Badge variant="secondary" className="text-xs">
                                {Math.round(doc.relevanceScore * 100)}% match
                              </Badge>
                            )}
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {formatTimeAgo(doc.metadata.timestamp)}
                          </span>
                        </div>

                        <p className="text-sm leading-relaxed line-clamp-3">
                          {doc.content}
                        </p>

                        {doc.metadata.title && (
                          <div className="text-xs text-muted-foreground">
                            Title: {doc.metadata.title}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>

            <TabsContent value="recent" className="mt-4">
              <ScrollArea className="h-[500px] pr-4">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="w-6 h-6 animate-spin" />
                  </div>
                ) : recentDocuments.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <MessageSquare className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No recent context</p>
                    <p className="text-sm">Start chatting to build context</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {recentDocuments.map((doc) => (
                      <div
                        key={doc.id}
                        className="p-4 border rounded-lg space-y-3 hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-2">
                            <div className={`w-6 h-6 rounded-full ${getTypeColor(doc.metadata.type)} flex items-center justify-center text-white`}>
                              {getTypeIcon(doc.metadata.type)}
                            </div>
                            <Badge variant="outline" className="text-xs capitalize">
                              {doc.metadata.type}
                            </Badge>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {formatTimeAgo(doc.metadata.timestamp)}
                          </span>
                        </div>

                        <p className="text-sm leading-relaxed line-clamp-3">
                          {doc.content}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  );
}
