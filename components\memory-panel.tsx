'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Brain, 
  Clock, 
  Tag, 
  User, 
  MessageSquare,
  Trash2,
  RefreshCw,
} from 'lucide-react';

interface MemoryItem {
  id: string;
  type: 'conversation' | 'fact' | 'preference' | 'context';
  content: string;
  timestamp: Date;
  relevance: number;
  tags: string[];
  source: string;
}

interface MemoryPanelProps {
  chatId: string;
  userId: string;
  agentId: string;
}

export function MemoryPanel({ chatId, userId, agentId }: MemoryPanelProps) {
  const [memories, setMemories] = useState<MemoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('recent');

  // Mock data - replace with actual API calls
  const mockMemories: MemoryItem[] = [
    {
      id: '1',
      type: 'preference',
      content: 'User prefers TypeScript over JavaScript for new projects',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      relevance: 0.9,
      tags: ['typescript', 'preference', 'coding'],
      source: 'conversation',
    },
    {
      id: '2',
      type: 'fact',
      content: 'User is working on a Next.js project with Tailwind CSS',
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      relevance: 0.8,
      tags: ['nextjs', 'tailwind', 'project'],
      source: 'conversation',
    },
    {
      id: '3',
      type: 'context',
      content: 'User mentioned they are a senior developer with 5+ years experience',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
      relevance: 0.7,
      tags: ['experience', 'senior', 'background'],
      source: 'conversation',
    },
    {
      id: '4',
      type: 'conversation',
      content: 'Discussed implementing authentication with NextAuth.js',
      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      relevance: 0.85,
      tags: ['auth', 'nextauth', 'implementation'],
      source: 'conversation',
    },
  ];

  useEffect(() => {
    // Load memories when component mounts
    loadMemories();
  }, [chatId, userId, agentId]);

  const loadMemories = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/memory/${userId}/${agentId}`);
      // const data = await response.json();
      // setMemories(data.memories);
      
      // For now, use mock data
      setTimeout(() => {
        setMemories(mockMemories);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to load memories:', error);
      setLoading(false);
    }
  };

  const clearMemories = async () => {
    if (!confirm('Are you sure you want to clear all memories? This action cannot be undone.')) {
      return;
    }
    
    try {
      // TODO: Implement API call to clear memories
      setMemories([]);
    } catch (error) {
      console.error('Failed to clear memories:', error);
    }
  };

  const getMemoryIcon = (type: MemoryItem['type']) => {
    switch (type) {
      case 'conversation':
        return <MessageSquare className="w-4 h-4" />;
      case 'fact':
        return <Tag className="w-4 h-4" />;
      case 'preference':
        return <User className="w-4 h-4" />;
      case 'context':
        return <Brain className="w-4 h-4" />;
      default:
        return <Brain className="w-4 h-4" />;
    }
  };

  const getMemoryColor = (type: MemoryItem['type']) => {
    switch (type) {
      case 'conversation':
        return 'bg-blue-500';
      case 'fact':
        return 'bg-green-500';
      case 'preference':
        return 'bg-purple-500';
      case 'context':
        return 'bg-orange-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const filteredMemories = memories.filter(memory => {
    switch (activeTab) {
      case 'recent':
        return true;
      case 'preferences':
        return memory.type === 'preference';
      case 'facts':
        return memory.type === 'fact';
      case 'context':
        return memory.type === 'context';
      default:
        return true;
    }
  }).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          <Brain className="w-4 h-4" />
          Memory
          {memories.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {memories.length}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Brain className="w-5 h-5" />
            Agent Memory
          </SheetTitle>
          <SheetDescription>
            View what the agent remembers about you and your conversations.
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-4">
          <div className="flex items-center justify-between">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="recent">Recent</TabsTrigger>
                <TabsTrigger value="preferences">Prefs</TabsTrigger>
                <TabsTrigger value="facts">Facts</TabsTrigger>
                <TabsTrigger value="context">Context</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={loadMemories}
              disabled={loading}
              className="gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearMemories}
              className="gap-2 text-destructive hover:text-destructive"
            >
              <Trash2 className="w-4 h-4" />
              Clear All
            </Button>
          </div>

          <ScrollArea className="h-[500px] pr-4">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="w-6 h-6 animate-spin" />
              </div>
            ) : filteredMemories.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Brain className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No memories found</p>
                <p className="text-sm">Start chatting to build memory</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredMemories.map((memory) => (
                  <div
                    key={memory.id}
                    className="p-4 border rounded-lg space-y-3 hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-6 h-6 rounded-full ${getMemoryColor(memory.type)} flex items-center justify-center text-white`}>
                          {getMemoryIcon(memory.type)}
                        </div>
                        <Badge variant="outline" className="text-xs capitalize">
                          {memory.type}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Clock className="w-3 h-3" />
                        {formatTimeAgo(memory.timestamp)}
                      </div>
                    </div>

                    <p className="text-sm leading-relaxed">{memory.content}</p>

                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {memory.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Relevance: {Math.round(memory.relevance * 100)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      </SheetContent>
    </Sheet>
  );
}
