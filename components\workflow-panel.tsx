'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import { 
  Workflow, 
  Play, 
  Pause, 
  Square, 
  CheckCircle, 
  AlertCircle,
  Clock,
  Zap,
} from 'lucide-react';

interface WorkflowStep {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
  result?: any;
  error?: string;
}

interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  steps: WorkflowStep[];
  startTime: Date;
  endTime?: Date;
  result?: any;
}

const availableWorkflows = [
  {
    id: 'research',
    name: 'Comprehensive Research',
    description: 'Conducts thorough research with fact-checking and source verification',
    estimatedTime: '3-5 minutes',
    complexity: 'high',
    inputs: [
      { name: 'topic', label: 'Research Topic', type: 'text', required: true },
      { name: 'depth', label: 'Research Depth', type: 'select', options: ['basic', 'detailed', 'comprehensive'], default: 'detailed' },
      { name: 'timeframe', label: 'Time Period', type: 'text', placeholder: 'e.g., last 5 years' },
    ],
  },
  {
    id: 'codeReview',
    name: 'Code Review',
    description: 'Comprehensive code analysis including security, performance, and quality checks',
    estimatedTime: '2-4 minutes',
    complexity: 'medium',
    inputs: [
      { name: 'code', label: 'Code to Review', type: 'textarea', required: true },
      { name: 'language', label: 'Programming Language', type: 'text', required: true },
      { name: 'reviewType', label: 'Review Type', type: 'select', options: ['security', 'performance', 'maintainability', 'comprehensive'], default: 'comprehensive' },
      { name: 'strictness', label: 'Review Strictness', type: 'select', options: ['lenient', 'standard', 'strict'], default: 'standard' },
    ],
  },
];

interface WorkflowPanelProps {
  chatId: string;
  onWorkflowResult?: (result: any) => void;
}

export function WorkflowPanel({ chatId, onWorkflowResult }: WorkflowPanelProps) {
  const [selectedWorkflow, setSelectedWorkflow] = useState<string>('');
  const [workflowInputs, setWorkflowInputs] = useState<Record<string, any>>({});
  const [currentExecution, setCurrentExecution] = useState<WorkflowExecution | null>(null);
  const [executionHistory, setExecutionHistory] = useState<WorkflowExecution[]>([]);

  const workflow = availableWorkflows.find(w => w.id === selectedWorkflow);

  const handleInputChange = (inputName: string, value: any) => {
    setWorkflowInputs(prev => ({
      ...prev,
      [inputName]: value,
    }));
  };

  const executeWorkflow = async () => {
    if (!workflow) return;

    const execution: WorkflowExecution = {
      id: `exec-${Date.now()}`,
      workflowId: workflow.id,
      status: 'running',
      steps: [
        { id: 'step-1', name: 'Initializing', status: 'running', progress: 0 },
        { id: 'step-2', name: 'Processing', status: 'pending' },
        { id: 'step-3', name: 'Analysis', status: 'pending' },
        { id: 'step-4', name: 'Finalizing', status: 'pending' },
      ],
      startTime: new Date(),
    };

    setCurrentExecution(execution);

    try {
      // Mock workflow execution - replace with actual API call
      const response = await fetch('/api/workflows/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          workflowId: workflow.id,
          inputs: workflowInputs,
          chatId,
        }),
      });

      if (!response.ok) {
        throw new Error('Workflow execution failed');
      }

      const result = await response.json();
      
      const completedExecution: WorkflowExecution = {
        ...execution,
        status: 'completed',
        endTime: new Date(),
        result: result.data,
        steps: execution.steps.map(step => ({ ...step, status: 'completed', progress: 100 })),
      };

      setCurrentExecution(completedExecution);
      setExecutionHistory(prev => [completedExecution, ...prev]);
      
      if (onWorkflowResult) {
        onWorkflowResult(result.data);
      }

    } catch (error) {
      const failedExecution: WorkflowExecution = {
        ...execution,
        status: 'failed',
        endTime: new Date(),
        steps: execution.steps.map((step, index) => ({
          ...step,
          status: index === 0 ? 'failed' : 'pending',
          error: index === 0 ? error.message : undefined,
        })),
      };

      setCurrentExecution(failedExecution);
      setExecutionHistory(prev => [failedExecution, ...prev]);
    }
  };

  const getStatusIcon = (status: WorkflowStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low':
        return 'bg-green-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'high':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          <Workflow className="w-4 h-4" />
          Workflows
          {currentExecution?.status === 'running' && (
            <Badge variant="secondary" className="animate-pulse">
              Running
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[500px] sm:w-[600px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <Workflow className="w-5 h-5" />
            Workflow Automation
          </SheetTitle>
          <SheetDescription>
            Execute complex multi-step workflows for research, analysis, and automation.
          </SheetDescription>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {/* Workflow Selection */}
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Select Workflow</label>
              <Select value={selectedWorkflow} onValueChange={setSelectedWorkflow}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a workflow..." />
                </SelectTrigger>
                <SelectContent>
                  {availableWorkflows.map((wf) => (
                    <SelectItem key={wf.id} value={wf.id}>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${getComplexityColor(wf.complexity)}`} />
                        <span>{wf.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {workflow && (
              <div className="p-4 border rounded-lg space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">{workflow.name}</h3>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {workflow.estimatedTime}
                    </Badge>
                    <Badge variant="outline" className="text-xs capitalize">
                      {workflow.complexity}
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">{workflow.description}</p>
              </div>
            )}
          </div>

          {/* Workflow Inputs */}
          {workflow && (
            <div className="space-y-4">
              <h3 className="font-medium">Configuration</h3>
              {workflow.inputs.map((input) => (
                <div key={input.name} className="space-y-2">
                  <label className="text-sm font-medium">
                    {input.label}
                    {input.required && <span className="text-red-500 ml-1">*</span>}
                  </label>
                  
                  {input.type === 'text' && (
                    <input
                      type="text"
                      className="w-full px-3 py-2 border rounded-md"
                      placeholder={input.placeholder}
                      value={workflowInputs[input.name] || ''}
                      onChange={(e) => handleInputChange(input.name, e.target.value)}
                    />
                  )}
                  
                  {input.type === 'textarea' && (
                    <Textarea
                      className="min-h-[100px]"
                      placeholder={input.placeholder}
                      value={workflowInputs[input.name] || ''}
                      onChange={(e) => handleInputChange(input.name, e.target.value)}
                    />
                  )}
                  
                  {input.type === 'select' && (
                    <Select
                      value={workflowInputs[input.name] || input.default}
                      onValueChange={(value) => handleInputChange(input.name, value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {input.options?.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Execution Controls */}
          {workflow && (
            <div className="flex gap-2">
              <Button
                onClick={executeWorkflow}
                disabled={currentExecution?.status === 'running'}
                className="gap-2"
              >
                <Play className="w-4 h-4" />
                {currentExecution?.status === 'running' ? 'Running...' : 'Execute Workflow'}
              </Button>
              
              {currentExecution?.status === 'running' && (
                <Button variant="outline" size="sm" className="gap-2">
                  <Square className="w-4 h-4" />
                  Stop
                </Button>
              )}
            </div>
          )}

          {/* Current Execution Status */}
          {currentExecution && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium">Execution Status</h3>
                <Badge variant={currentExecution.status === 'completed' ? 'default' : 'secondary'}>
                  {currentExecution.status}
                </Badge>
              </div>
              
              <div className="space-y-2">
                {currentExecution.steps.map((step, index) => (
                  <div key={step.id} className="flex items-center gap-3 p-2 border rounded">
                    {getStatusIcon(step.status)}
                    <div className="flex-1">
                      <div className="text-sm font-medium">{step.name}</div>
                      {step.progress !== undefined && (
                        <Progress value={step.progress} className="h-1 mt-1" />
                      )}
                      {step.error && (
                        <div className="text-xs text-red-500 mt-1">{step.error}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Execution History */}
          {executionHistory.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-medium">Recent Executions</h3>
              <ScrollArea className="h-[200px]">
                <div className="space-y-2">
                  {executionHistory.map((execution) => (
                    <div key={execution.id} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium">
                          {availableWorkflows.find(w => w.id === execution.workflowId)?.name}
                        </div>
                        <Badge variant={execution.status === 'completed' ? 'default' : 'destructive'}>
                          {execution.status}
                        </Badge>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {execution.startTime.toLocaleString()}
                        {execution.endTime && (
                          <span> • Duration: {Math.round((execution.endTime.getTime() - execution.startTime.getTime()) / 1000)}s</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
