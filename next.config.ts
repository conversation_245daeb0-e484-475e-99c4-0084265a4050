import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  experimental: {
    ppr: true,
  },
  images: {
    remotePatterns: [
      {
        hostname: 'avatar.vercel.sh',
      },
    ],
  },
  // Improve hydration handling
  reactStrictMode: true,
  // Suppress hydration warnings in development for known browser extension issues
  ...(process.env.NODE_ENV === 'development' && {
    onDemandEntries: {
      // Extend the timeout for hydration
      maxInactiveAge: 60 * 1000,
      pagesBufferLength: 5,
    },
  }),
};

export default nextConfig;
