import { DefaultEmbeddingFunction } from '@chroma-core/default-embed';
import { CloudClient } from 'chromadb';
import { nanoid } from 'nanoid';

export interface RAGDocument {
  id: string;
  content: string;
  metadata: {
    userId: string;
    chatId: string;
    messageId?: string;
    timestamp: number;
    type: 'message' | 'context' | 'knowledge';
    source?: string;
    title?: string;
  };
}

export interface RAGSearchResult {
  id: string;
  content: string;
  metadata: RAGDocument['metadata'];
  distance: number;
  relevanceScore: number;
}

export class ChromaRAGService {
  private client: CloudClient;
  private embeddingFunction: DefaultEmbeddingFunction;
  private collectionCache = new Map<string, any>();

  constructor() {
    // Use hardcoded values if environment variables are not available
    const apiKey =
      process.env.CHROMA_API_KEY ||
      'ck-5X831whg7iha9NaDnPFGxF2KyGQLVttGeHCjsQFi3ZDi';
    const tenant =
      process.env.CHROMA_TENANT || 'ae085e38-67c9-4d61-a73b-328ddbe3ee4c';
    const database = process.env.CHROMA_DATABASE || 'vovagpt';

    this.client = new CloudClient({
      apiKey,
      tenant,
      database,
    });

    this.embeddingFunction = new DefaultEmbeddingFunction();
  }

  /**
   * Get or create a collection for a specific user and chat session
   */
  private async getCollection(userId: string, chatId: string) {
    const collectionName = `user_${userId}_chat_${chatId}`;
    const cacheKey = collectionName;

    if (this.collectionCache.has(cacheKey)) {
      return this.collectionCache.get(cacheKey);
    }

    try {
      // Try to get existing collection
      const collection = await this.client.getCollection({
        name: collectionName,
        embeddingFunction: this.embeddingFunction,
      });

      this.collectionCache.set(cacheKey, collection);
      return collection;
    } catch (error) {
      // Collection doesn't exist, create it
      const collection = await this.client.createCollection({
        name: collectionName,
        embeddingFunction: this.embeddingFunction,
        metadata: {
          userId,
          chatId,
          createdAt: Date.now(),
          description: `RAG collection for user ${userId} chat ${chatId}`,
        },
      });

      this.collectionCache.set(cacheKey, collection);
      return collection;
    }
  }

  /**
   * Add a document to the RAG system
   */
  async addDocument(document: Omit<RAGDocument, 'id'>): Promise<string> {
    const id = nanoid();
    const fullDocument: RAGDocument = { id, ...document };

    const collection = await this.getCollection(
      document.metadata.userId,
      document.metadata.chatId,
    );

    await collection.add({
      ids: [id],
      documents: [document.content],
      metadatas: [document.metadata],
    });

    return id;
  }

  /**
   * Add multiple documents in batch
   */
  async addDocuments(documents: Omit<RAGDocument, 'id'>[]): Promise<string[]> {
    if (documents.length === 0) return [];

    const ids = documents.map(() => nanoid());
    const fullDocuments = documents.map((doc, index) => ({
      id: ids[index],
      ...doc,
    }));

    // Group by user and chat for efficient batch operations
    const groupedDocs = new Map<string, typeof fullDocuments>();

    for (const doc of fullDocuments) {
      const key = `${doc.metadata.userId}_${doc.metadata.chatId}`;
      if (!groupedDocs.has(key)) {
        groupedDocs.set(key, []);
      }
      groupedDocs.get(key)!.push(doc);
    }

    // Process each group
    for (const [key, docs] of groupedDocs) {
      const [userId, chatId] = key.split('_');
      const collection = await this.getCollection(userId, chatId);

      await collection.add({
        ids: docs.map((d) => d.id),
        documents: docs.map((d) => d.content),
        metadatas: docs.map((d) => d.metadata),
      });
    }

    return ids;
  }

  /**
   * Search for relevant documents
   */
  async searchDocuments(
    query: string,
    userId: string,
    chatId: string,
    options: {
      limit?: number;
      minRelevanceScore?: number;
      includeTypes?: Array<'message' | 'context' | 'knowledge'>;
      timeRange?: { start?: number; end?: number };
    } = {},
  ): Promise<RAGSearchResult[]> {
    const {
      limit = 10,
      minRelevanceScore = 0.3,
      includeTypes,
      timeRange,
    } = options;

    const collection = await this.getCollection(userId, chatId);

    // Build where clause for filtering
    const whereClause: any = {};

    if (includeTypes && includeTypes.length > 0) {
      whereClause.type = { $in: includeTypes };
    }

    if (timeRange) {
      const timestampFilter: any = {};
      if (timeRange.start) timestampFilter.$gte = timeRange.start;
      if (timeRange.end) timestampFilter.$lte = timeRange.end;
      if (Object.keys(timestampFilter).length > 0) {
        whereClause.timestamp = timestampFilter;
      }
    }

    const results = await collection.query({
      queryTexts: [query],
      nResults: limit * 2, // Get more results to filter by relevance
      where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
    });

    if (!results.documents || !results.documents[0]) {
      return [];
    }

    // Process and score results
    const searchResults: RAGSearchResult[] = [];
    const documents = results.documents[0];
    const metadatas = results.metadatas?.[0] || [];
    const distances = results.distances?.[0] || [];
    const ids = results.ids?.[0] || [];

    for (let i = 0; i < documents.length; i++) {
      const distance = distances[i] || 1;
      const relevanceScore = Math.max(0, 1 - distance); // Convert distance to relevance score

      if (relevanceScore >= minRelevanceScore) {
        searchResults.push({
          id: ids[i],
          content: documents[i],
          metadata: metadatas[i] as RAGDocument['metadata'],
          distance,
          relevanceScore,
        });
      }
    }

    // Sort by relevance score (highest first) and limit results
    return searchResults
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limit);
  }

  /**
   * Get conversation context for a chat session
   */
  async getConversationContext(
    userId: string,
    chatId: string,
    limit: number = 20,
  ): Promise<RAGSearchResult[]> {
    const collection = await this.getCollection(userId, chatId);

    try {
      const results = await collection.get({
        where: { type: 'message' },
        limit,
        orderBy: 'timestamp',
      });

      if (!results.documents) return [];

      return results.documents.map((doc, index) => ({
        id: results.ids?.[index] || '',
        content: doc,
        metadata: results.metadatas?.[index] as RAGDocument['metadata'],
        distance: 0,
        relevanceScore: 1,
      }));
    } catch (error) {
      console.error('Error getting conversation context:', error);
      return [];
    }
  }

  /**
   * Delete documents by ID
   */
  async deleteDocuments(
    documentIds: string[],
    userId: string,
    chatId: string,
  ): Promise<void> {
    if (documentIds.length === 0) return;

    const collection = await this.getCollection(userId, chatId);
    await collection.delete({ ids: documentIds });
  }

  /**
   * Clear all documents for a chat session
   */
  async clearChatSession(userId: string, chatId: string): Promise<void> {
    try {
      const collectionName = `user_${userId}_chat_${chatId}`;
      await this.client.deleteCollection({ name: collectionName });
      this.collectionCache.delete(collectionName);
    } catch (error) {
      console.error('Error clearing chat session:', error);
    }
  }

  /**
   * Get collection statistics
   */
  async getCollectionStats(userId: string, chatId: string) {
    try {
      const collection = await this.getCollection(userId, chatId);
      const count = await collection.count();

      return {
        documentCount: count,
        collectionName: `user_${userId}_chat_${chatId}`,
        userId,
        chatId,
      };
    } catch (error) {
      console.error('Error getting collection stats:', error);
      return {
        documentCount: 0,
        collectionName: `user_${userId}_chat_${chatId}`,
        userId,
        chatId,
      };
    }
  }

  /**
   * Health check for ChromaDB connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.client.heartbeat();
      return true;
    } catch (error) {
      console.error('ChromaDB health check failed:', error);
      return false;
    }
  }
}

// Singleton instance
export const chromaRAGService = new ChromaRAGService();
