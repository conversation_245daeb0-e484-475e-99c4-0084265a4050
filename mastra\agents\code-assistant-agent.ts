import { openai } from '@ai-sdk/openai';
import { Agent } from '@mastra/core';
import { Memory } from '@mastra/memory';

export const codeAssistantAgent = new Agent({
  name: 'Code Assistant',
  instructions: `You are an expert software engineer and coding assistant. You specialize in:

1. **Code Generation**: Writing clean, efficient, and well-documented code
2. **Code Review**: Analyzing code for bugs, performance issues, and best practices
3. **Debugging**: Helping identify and fix issues in code
4. **Architecture**: Providing guidance on software design and architecture
5. **Best Practices**: Recommending industry standards and coding conventions
6. **Multiple Languages**: Proficient in JavaScript/TypeScript, Python, Java, C#, Go, Rust, and more

Key behaviors:
- Always provide working, tested code examples
- Explain your reasoning and approach
- Consider security, performance, and maintainability
- Use modern best practices and patterns
- Provide multiple solutions when appropriate
- Include relevant comments and documentation
- Test code when possible using available tools

When helping with code:
- Ask for clarification on requirements if needed
- Provide complete, runnable examples
- Explain complex concepts clearly
- Suggest improvements and optimizations
- Consider edge cases and error handling`,

  model: openai('gpt-4o'),

  memory: new Memory({
    provider: 'postgres',
    config: {
      connectionString: process.env.POSTGRES_URL,
    },
  }),

  // Dynamic configuration for different programming contexts
  dynamicConfig: {
    instructions: (context) => {
      const projectType = context.get('projectType');
      const programmingLanguage = context.get('programmingLanguage');

      let instructions = `You are an expert software engineer.`;

      if (programmingLanguage) {
        instructions += ` You are currently working with ${programmingLanguage}.`;
      }

      if (projectType === 'web') {
        instructions += ` Focus on web development best practices, including responsive design, accessibility, and performance optimization.`;
      } else if (projectType === 'mobile') {
        instructions += ` Focus on mobile development patterns, performance, and user experience.`;
      } else if (projectType === 'backend') {
        instructions += ` Focus on server-side development, API design, database optimization, and scalability.`;
      }

      return instructions;
    },
  },

  // Specialized tools for code assistance
  tools: [
    'codeExecution',
    'fileAnalysis',
    'webSearch',
    'ragSearch',
    'ragContext',
  ],

  defaultGenerateOptions: {
    maxSteps: 5,
    temperature: 0.3, // Lower temperature for more consistent code generation
  },
});
