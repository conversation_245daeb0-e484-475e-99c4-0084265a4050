'use client';

import { useSuppressExtensionHydration } from '@/hooks/use-suppress-extension-hydration';
import type { ReactNode } from 'react';

interface HydrationBoundaryProps {
  children: ReactNode;
}

/**
 * Component that handles hydration issues caused by browser extensions
 */
export function HydrationBoundary({ children }: HydrationBoundaryProps) {
  useSuppressExtensionHydration();
  
  return <>{children}</>;
}
