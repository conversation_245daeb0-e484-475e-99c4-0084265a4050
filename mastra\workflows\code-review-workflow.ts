import { Workflow, createStep } from '@mastra/core';
import { z } from 'zod';

const codeReviewInputSchema = z.object({
  code: z.string().describe('The code to review'),
  language: z.string().describe('Programming language'),
  context: z.string().optional().describe('Additional context about the code'),
  reviewType: z.enum(['security', 'performance', 'maintainability', 'comprehensive']).default('comprehensive'),
  strictness: z.enum(['lenient', 'standard', 'strict']).default('standard'),
});

const codeReviewOutputSchema = z.object({
  overallScore: z.number().min(0).max(10),
  summary: z.string(),
  issues: z.array(z.object({
    type: z.enum(['bug', 'security', 'performance', 'style', 'maintainability']),
    severity: z.enum(['low', 'medium', 'high', 'critical']),
    line: z.number().optional(),
    description: z.string(),
    suggestion: z.string(),
    example: z.string().optional(),
  })),
  strengths: z.array(z.string()),
  recommendations: z.array(z.string()),
  refactoredCode: z.string().optional(),
});

export const codeReviewWorkflow = new Workflow({
  name: 'Comprehensive Code Review',
  description: 'Performs thorough code review with multiple analysis passes',
  
  inputSchema: codeReviewInputSchema,
  outputSchema: codeReviewOutputSchema,

  steps: [
    // Step 1: Static analysis and syntax check
    createStep({
      id: 'static-analysis',
      name: 'Static Analysis',
      description: 'Perform static code analysis and syntax validation',
      
      execute: async ({ code, language }, { tools, runtimeContext }) => {
        try {
          // Use file analysis tool to understand code structure
          const analysis = await tools.fileAnalysis({
            filePath: `/tmp/code-review.${getFileExtension(language)}`,
            analysisType: 'structure',
          });

          // Try to execute/validate the code if possible
          let executionResult = null;
          if (['javascript', 'typescript', 'python'].includes(language.toLowerCase())) {
            try {
              executionResult = await tools.codeExecution({
                code,
                language: language.toLowerCase() as 'javascript' | 'typescript' | 'python',
                timeout: 5000,
              });
            } catch (error) {
              executionResult = { error: error.message };
            }
          }

          return {
            structure: analysis.structure,
            syntaxValid: !executionResult?.error,
            executionError: executionResult?.error,
            codeMetrics: {
              lines: code.split('\n').length,
              functions: analysis.structure?.functions?.length || 0,
              classes: analysis.structure?.classes?.length || 0,
              complexity: estimateComplexity(code),
            },
          };
        } catch (error) {
          return {
            structure: null,
            syntaxValid: false,
            executionError: error.message,
            codeMetrics: {
              lines: code.split('\n').length,
              functions: 0,
              classes: 0,
              complexity: 'unknown',
            },
          };
        }
      },
    }),

    // Step 2: Security analysis
    createStep({
      id: 'security-analysis',
      name: 'Security Analysis',
      description: 'Analyze code for security vulnerabilities',
      
      execute: async ({ code, language, reviewType }, { agents, runtimeContext }) => {
        if (reviewType !== 'security' && reviewType !== 'comprehensive') {
          return { securityIssues: [], securityScore: 10 };
        }

        const codeAgent = agents.codeAssistant;
        
        const securityAnalysis = await codeAgent.generate([
          {
            role: 'user',
            content: `Perform a security analysis of this ${language} code:

\`\`\`${language}
${code}
\`\`\`

Identify potential security vulnerabilities including:
- SQL injection risks
- XSS vulnerabilities
- Authentication/authorization issues
- Input validation problems
- Cryptographic issues
- Information disclosure
- CSRF vulnerabilities

For each issue found, provide:
- Severity level (low/medium/high/critical)
- Line number if applicable
- Description of the vulnerability
- Suggested fix`,
          },
        ], {
          output: z.object({
            issues: z.array(z.object({
              type: z.string(),
              severity: z.enum(['low', 'medium', 'high', 'critical']),
              line: z.number().optional(),
              description: z.string(),
              suggestion: z.string(),
            })),
            overallSecurityScore: z.number().min(0).max(10),
            summary: z.string(),
          }),
        });

        return {
          securityIssues: securityAnalysis.object.issues,
          securityScore: securityAnalysis.object.overallSecurityScore,
          securitySummary: securityAnalysis.object.summary,
        };
      },
    }),

    // Step 3: Performance analysis
    createStep({
      id: 'performance-analysis',
      name: 'Performance Analysis',
      description: 'Analyze code for performance issues and optimizations',
      
      execute: async ({ code, language, reviewType }, { agents, runtimeContext }) => {
        if (reviewType !== 'performance' && reviewType !== 'comprehensive') {
          return { performanceIssues: [], performanceScore: 10 };
        }

        const codeAgent = agents.codeAssistant;
        
        const performanceAnalysis = await codeAgent.generate([
          {
            role: 'user',
            content: `Analyze this ${language} code for performance issues:

\`\`\`${language}
${code}
\`\`\`

Look for:
- Inefficient algorithms or data structures
- Memory leaks or excessive memory usage
- Unnecessary loops or iterations
- Database query optimization opportunities
- Caching opportunities
- Async/await usage issues
- Resource management problems

Rate performance from 1-10 and suggest optimizations.`,
          },
        ], {
          output: z.object({
            issues: z.array(z.object({
              type: z.string(),
              severity: z.enum(['low', 'medium', 'high']),
              line: z.number().optional(),
              description: z.string(),
              suggestion: z.string(),
              impact: z.string(),
            })),
            performanceScore: z.number().min(0).max(10),
            optimizations: z.array(z.string()),
          }),
        });

        return {
          performanceIssues: performanceAnalysis.object.issues,
          performanceScore: performanceAnalysis.object.performanceScore,
          optimizations: performanceAnalysis.object.optimizations,
        };
      },
    }),

    // Step 4: Code quality and maintainability
    createStep({
      id: 'quality-analysis',
      name: 'Quality Analysis',
      description: 'Analyze code quality, style, and maintainability',
      
      execute: async ({ code, language, strictness }, { agents, runtimeContext }) => {
        const codeAgent = agents.codeAssistant;
        
        const qualityAnalysis = await codeAgent.generate([
          {
            role: 'user',
            content: `Review this ${language} code for quality and maintainability (${strictness} standards):

\`\`\`${language}
${code}
\`\`\`

Evaluate:
- Code organization and structure
- Naming conventions
- Comments and documentation
- Error handling
- Code duplication
- Function/method length and complexity
- Adherence to best practices
- Testability

Provide specific suggestions for improvement.`,
          },
        ], {
          output: z.object({
            qualityScore: z.number().min(0).max(10),
            issues: z.array(z.object({
              category: z.enum(['style', 'structure', 'naming', 'documentation', 'error-handling']),
              severity: z.enum(['low', 'medium', 'high']),
              line: z.number().optional(),
              description: z.string(),
              suggestion: z.string(),
            })),
            strengths: z.array(z.string()),
            improvements: z.array(z.string()),
          }),
        });

        return {
          qualityScore: qualityAnalysis.object.qualityScore,
          qualityIssues: qualityAnalysis.object.issues,
          strengths: qualityAnalysis.object.strengths,
          improvements: qualityAnalysis.object.improvements,
        };
      },
    }),

    // Step 5: Generate refactored code (optional)
    createStep({
      id: 'refactor-code',
      name: 'Generate Refactored Code',
      description: 'Generate improved version of the code based on findings',
      
      execute: async ({ code, language, securityIssues, performanceIssues, qualityIssues }, { agents, runtimeContext }) => {
        const codeAgent = agents.codeAssistant;
        
        // Only refactor if there are significant issues
        const totalIssues = (securityIssues?.length || 0) + (performanceIssues?.length || 0) + (qualityIssues?.length || 0);
        
        if (totalIssues < 3) {
          return { refactoredCode: null, refactoringSummary: 'Code quality is good, no refactoring needed.' };
        }

        const allIssues = [
          ...(securityIssues || []),
          ...(performanceIssues || []),
          ...(qualityIssues || []),
        ];

        const refactoring = await codeAgent.generate([
          {
            role: 'user',
            content: `Refactor this ${language} code to address the following issues:

Original code:
\`\`\`${language}
${code}
\`\`\`

Issues to address:
${allIssues.map(issue => `- ${issue.description}: ${issue.suggestion}`).join('\n')}

Provide the refactored code and explain the changes made.`,
          },
        ], {
          output: z.object({
            refactoredCode: z.string(),
            changes: z.array(z.string()),
            summary: z.string(),
          }),
        });

        return {
          refactoredCode: refactoring.object.refactoredCode,
          refactoringSummary: refactoring.object.summary,
          changesMade: refactoring.object.changes,
        };
      },
    }),

    // Step 6: Compile final report
    createStep({
      id: 'compile-report',
      name: 'Compile Final Report',
      description: 'Compile all analysis results into a comprehensive report',
      
      execute: async ({ 
        code, 
        language, 
        codeMetrics, 
        securityScore, 
        securityIssues, 
        performanceScore, 
        performanceIssues, 
        qualityScore, 
        qualityIssues, 
        strengths, 
        improvements,
        refactoredCode,
        refactoringSummary 
      }, { runtimeContext }) => {
        
        // Calculate overall score
        const scores = [securityScore || 10, performanceScore || 10, qualityScore || 10];
        const overallScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

        // Combine all issues
        const allIssues = [
          ...(securityIssues || []).map(issue => ({ ...issue, type: 'security' as const })),
          ...(performanceIssues || []).map(issue => ({ ...issue, type: 'performance' as const })),
          ...(qualityIssues || []).map(issue => ({ ...issue, type: issue.category as any })),
        ];

        // Generate summary
        const summary = `Code review completed for ${language} code (${codeMetrics.lines} lines). 
Overall score: ${overallScore.toFixed(1)}/10. 
Found ${allIssues.length} issues: ${allIssues.filter(i => i.severity === 'critical').length} critical, 
${allIssues.filter(i => i.severity === 'high').length} high, 
${allIssues.filter(i => i.severity === 'medium').length} medium, 
${allIssues.filter(i => i.severity === 'low').length} low severity.`;

        return {
          overallScore: Math.round(overallScore * 10) / 10,
          summary,
          issues: allIssues,
          strengths: strengths || [],
          recommendations: improvements || [],
          refactoredCode: refactoredCode || undefined,
          metadata: {
            linesOfCode: codeMetrics.lines,
            functions: codeMetrics.functions,
            classes: codeMetrics.classes,
            complexity: codeMetrics.complexity,
            reviewedAt: new Date().toISOString(),
          },
        };
      },
    }),
  ],
});

// Helper functions
function getFileExtension(language: string): string {
  const extensions: Record<string, string> = {
    javascript: 'js',
    typescript: 'ts',
    python: 'py',
    java: 'java',
    cpp: 'cpp',
    c: 'c',
    csharp: 'cs',
    go: 'go',
    rust: 'rs',
    php: 'php',
    ruby: 'rb',
  };
  
  return extensions[language.toLowerCase()] || 'txt';
}

function estimateComplexity(code: string): string {
  const lines = code.split('\n').length;
  const conditions = (code.match(/if|else|while|for|switch|case/g) || []).length;
  const functions = (code.match(/function|def|class/g) || []).length;
  
  const complexityScore = (conditions * 2) + (functions * 1) + (lines * 0.1);
  
  if (complexityScore < 10) return 'low';
  if (complexityScore < 25) return 'medium';
  if (complexityScore < 50) return 'high';
  return 'very high';
}
