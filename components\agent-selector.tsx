'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Code,
  FileText,
  MessageSquare,
  Search,
  Settings,
  Zap,
} from 'lucide-react';
import { useState } from 'react';

export interface Agent {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  capabilities: string[];
  color: string;
  premium?: boolean;
}

const agents: Agent[] = [
  {
    id: 'generalChat',
    name: 'General Assistant',
    description: 'Helpful AI assistant for general questions and conversations',
    icon: <MessageSquare className="w-4 h-4" />,
    capabilities: [
      'General Q&A',
      'Conversation',
      'Problem Solving',
      'Creative Writing',
    ],
    color: 'bg-blue-500',
  },
  {
    id: 'codeAssistant',
    name: 'Code Assistant',
    description:
      'Expert software engineer for coding, debugging, and architecture',
    icon: <Code className="w-4 h-4" />,
    capabilities: [
      'Code Generation',
      'Debugging',
      'Code Review',
      'Architecture',
    ],
    color: 'bg-green-500',
  },
  {
    id: 'research',
    name: 'Research Assistant',
    description: 'Thorough researcher for gathering and analyzing information',
    icon: <Search className="w-4 h-4" />,
    capabilities: [
      'Web Research',
      'Data Analysis',
      'Fact Checking',
      'Citations',
    ],
    color: 'bg-purple-500',
    premium: true,
  },
  {
    id: 'documentAnalysis',
    name: 'Document Analyst',
    description:
      'Specialist in analyzing and extracting insights from documents',
    icon: <FileText className="w-4 h-4" />,
    capabilities: [
      'Document Analysis',
      'Summarization',
      'Data Extraction',
      'Comparison',
    ],
    color: 'bg-orange-500',
    premium: true,
  },
];

interface AgentSelectorProps {
  selectedAgent: string;
  onAgentChange: (agentId: string) => void;
  userTier?: 'free' | 'premium';
  className?: string;
}

export function AgentSelector({
  selectedAgent,
  onAgentChange,
  userTier = 'free',
  className,
}: AgentSelectorProps) {
  const [showDetails, setShowDetails] = useState(false);

  const currentAgent =
    agents.find((agent) => agent.id === selectedAgent) || agents[0];
  const availableAgents = agents.filter(
    (agent) => !agent.premium || userTier === 'premium',
  );

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center gap-2">
              <div
                className={`w-8 h-8 rounded-full ${currentAgent.color} flex items-center justify-center text-white`}
              >
                {currentAgent.icon}
              </div>
              <div className="hidden sm:block">
                <div className="text-sm font-medium">{currentAgent.name}</div>
                <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                  {currentAgent.description}
                </div>
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent side="bottom" className="max-w-xs">
            <div className="space-y-2">
              <div className="font-medium">{currentAgent.name}</div>
              <div className="text-sm text-muted-foreground">
                {currentAgent.description}
              </div>
              <div className="flex flex-wrap gap-1">
                {currentAgent.capabilities.map((capability) => (
                  <Badge
                    key={capability}
                    variant="secondary"
                    className="text-xs"
                  >
                    {capability}
                  </Badge>
                ))}
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <Select value={selectedAgent} onValueChange={onAgentChange}>
        <SelectTrigger className="w-[180px]">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {availableAgents.map((agent) => (
            <SelectItem key={agent.id} value={agent.id}>
              <div className="flex items-center gap-2">
                <div
                  className={`w-4 h-4 rounded-full ${agent.color} flex items-center justify-center text-white text-xs`}
                >
                  {agent.icon}
                </div>
                <span>{agent.name}</span>
                {agent.premium && (
                  <Badge variant="outline" className="text-xs">
                    Pro
                  </Badge>
                )}
              </div>
            </SelectItem>
          ))}

          {userTier === 'free' && agents.some((agent) => agent.premium) && (
            <div className="px-2 py-1 text-xs text-muted-foreground border-t">
              <div className="flex items-center gap-1">
                <Zap className="w-3 h-3" />
                Upgrade for premium agents
              </div>
            </div>
          )}
        </SelectContent>
      </Select>

      <Button
        variant="ghost"
        size="sm"
        onClick={() => setShowDetails(!showDetails)}
        className="p-1"
      >
        <Settings className="w-4 h-4" />
      </Button>

      {showDetails && (
        <div className="absolute top-full left-0 mt-2 p-4 bg-background border rounded-lg shadow-lg z-50 min-w-[300px]">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <div
                className={`w-6 h-6 rounded-full ${currentAgent.color} flex items-center justify-center text-white`}
              >
                {currentAgent.icon}
              </div>
              <div>
                <div className="font-medium">{currentAgent.name}</div>
                {currentAgent.premium && (
                  <Badge variant="outline" className="text-xs">
                    Premium
                  </Badge>
                )}
              </div>
            </div>

            <div className="text-sm text-muted-foreground">
              {currentAgent.description}
            </div>

            <div>
              <div className="text-sm font-medium mb-2">Capabilities:</div>
              <div className="flex flex-wrap gap-1">
                {currentAgent.capabilities.map((capability) => (
                  <Badge
                    key={capability}
                    variant="secondary"
                    className="text-xs"
                  >
                    {capability}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export { agents };
