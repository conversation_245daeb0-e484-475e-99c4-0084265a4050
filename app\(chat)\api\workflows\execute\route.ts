import { NextRequest } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { mastra } from '@/mastra';
import { RuntimeContext } from '@mastra/core';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { workflowId, inputs, chatId } = await request.json();

    if (!workflowId || !inputs) {
      return new Response('Missing required fields', { status: 400 });
    }

    // Create runtime context
    const runtimeContext = new RuntimeContext();
    runtimeContext.set('userId', session.user.id);
    runtimeContext.set('userEmail', session.user.email);
    runtimeContext.set('chatId', chatId);

    // Get the workflow
    const workflow = mastra.getWorkflow(workflowId);
    
    if (!workflow) {
      return new Response(`Workflow "${workflowId}" not found`, { status: 404 });
    }

    // Execute the workflow
    const result = await workflow.execute(inputs, {
      runtimeContext,
      onStepStart: (stepId, stepName) => {
        console.log(`Workflow ${workflowId} - Step started: ${stepName} (${stepId})`);
      },
      onStepComplete: (stepId, stepName, result) => {
        console.log(`Workflow ${workflowId} - Step completed: ${stepName} (${stepId})`);
      },
      onStepError: (stepId, stepName, error) => {
        console.error(`Workflow ${workflowId} - Step failed: ${stepName} (${stepId})`, error);
      },
    });

    return Response.json({
      success: true,
      data: result,
      workflowId,
      executedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Workflow execution error:', error);
    
    return Response.json(
      {
        success: false,
        error: 'Workflow execution failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
