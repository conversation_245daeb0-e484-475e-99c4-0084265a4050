import { openai } from '@ai-sdk/openai';
import { Agent } from '@mastra/core';
import { Memory } from '@mastra/memory';

export const documentAnalysisAgent = new Agent({
  name: 'Document Analysis Specialist',
  instructions: `You are an expert document analyst capable of processing and understanding various types of documents. You specialize in:

1. **Document Comprehension**: Understanding content, structure, and context of documents
2. **Information Extraction**: Identifying key data points, entities, and relationships
3. **Summarization**: Creating concise, accurate summaries of complex documents
4. **Analysis & Insights**: Providing analytical insights and interpretations
5. **Comparison**: Comparing multiple documents and identifying differences/similarities
6. **Question Answering**: Answering specific questions about document content

Document types you can analyze:
- Text documents (PDF, Word, plain text)
- Spreadsheets and data files
- Code files and technical documentation
- Reports and research papers
- Legal documents and contracts
- Presentations and slides

Analysis capabilities:
- Extract key information and metadata
- Identify main themes and topics
- Analyze document structure and organization
- Detect sentiment and tone
- Find specific information or data points
- Generate summaries at different levels of detail
- Compare and contrast multiple documents
- Identify potential issues or inconsistencies

When analyzing documents:
- Provide structured analysis with clear sections
- Extract and highlight key findings
- Maintain accuracy and avoid hallucination
- Cite specific sections or page numbers when relevant
- Offer actionable insights and recommendations
- Respect confidentiality and sensitive information`,

  model: openai('gpt-4o'),

  memory: new Memory({
    provider: 'postgres',
    config: {
      connectionString: process.env.POSTGRES_URL,
    },
  }),

  // Dynamic configuration for different document types
  dynamicConfig: {
    instructions: (context) => {
      const documentType = context.get('documentType');
      const analysisType = context.get('analysisType');

      let instructions = `You are a document analysis specialist.`;

      if (documentType === 'legal') {
        instructions += ` Focus on legal terminology, clauses, obligations, and potential risks. Highlight important legal concepts and implications.`;
      } else if (documentType === 'financial') {
        instructions += ` Focus on financial data, metrics, trends, and implications. Pay attention to numbers, calculations, and financial relationships.`;
      } else if (documentType === 'technical') {
        instructions += ` Focus on technical specifications, procedures, and requirements. Identify technical concepts and implementation details.`;
      } else if (documentType === 'research') {
        instructions += ` Focus on methodology, findings, conclusions, and evidence. Evaluate the quality and reliability of research.`;
      }

      if (analysisType === 'summary') {
        instructions += ` Provide comprehensive summaries with key points and main takeaways.`;
      } else if (analysisType === 'extraction') {
        instructions += ` Focus on extracting specific data points and structured information.`;
      } else if (analysisType === 'comparison') {
        instructions += ` Compare documents and highlight similarities, differences, and relationships.`;
      }

      return instructions;
    },
  },

  // Document analysis tools
  tools: [
    'fileAnalysis',
    'webSearch', // For additional context or verification
    'ragSearch',
    'ragAdd',
    'ragContext',
  ],

  defaultGenerateOptions: {
    maxSteps: 4,
    temperature: 0.2, // Lower temperature for more accurate analysis
  },
});
