'use client';

import { useEffect } from 'react';

/**
 * Hook to suppress hydration warnings caused by browser extensions
 * that add attributes to DOM elements after server-side rendering
 */
export function useSuppressExtensionHydration() {
  useEffect(() => {
    // List of attributes commonly added by browser extensions
    const extensionAttributes = [
      'rtrvr-listeners',
      'data-extension-id',
      'data-reader-mode',
      'data-translate',
      'data-grammarly',
      'data-lt-installed',
      'data-honey-extension',
    ];

    // Find all elements with extension attributes and mark them
    const markExtensionElements = () => {
      extensionAttributes.forEach(attr => {
        const elements = document.querySelectorAll(`[${attr}]`);
        elements.forEach(element => {
          // Add a data attribute to identify extension-modified elements
          element.setAttribute('data-extension-modified', 'true');
        });
      });
    };

    // Run immediately and on DOM mutations
    markExtensionElements();

    // Set up a mutation observer to catch dynamically added attributes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes') {
          const target = mutation.target as Element;
          const attributeName = mutation.attributeName;
          
          if (attributeName && extensionAttributes.includes(attributeName)) {
            target.setAttribute('data-extension-modified', 'true');
          }
        }
      });
    });

    // Observe the entire document for attribute changes
    observer.observe(document.body, {
      attributes: true,
      subtree: true,
      attributeFilter: extensionAttributes,
    });

    return () => {
      observer.disconnect();
    };
  }, []);
}
