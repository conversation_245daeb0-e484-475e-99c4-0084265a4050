import { NextRequest } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { chromaRAGService } from '@/lib/rag/chroma-service';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { query, chatId, userId, limit = 10, minRelevanceScore = 0.3, includeTypes } = await request.json();

    if (!query || !chatId || !userId) {
      return new Response('Missing required fields', { status: 400 });
    }

    // Verify user can access this chat
    if (userId !== session.user.id) {
      return new Response('Forbidden', { status: 403 });
    }

    const results = await chromaRAGService.searchDocuments(
      query,
      userId,
      chatId,
      {
        limit: Math.min(Math.max(limit, 1), 50),
        minRelevanceScore: Math.max(0, Math.min(minRelevanceScore, 1)),
        includeTypes,
      }
    );

    return Response.json({
      success: true,
      results,
      query,
      totalResults: results.length,
    });

  } catch (error) {
    console.error('RAG search error:', error);
    
    return Response.json(
      {
        success: false,
        error: 'RAG search failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
