import { openai } from '@ai-sdk/openai';
import { Agent } from '@mastra/core';
import { Memory } from '@mastra/memory';

export const generalChatAgent = new Agent({
  name: 'General Chat Assistant',
  instructions: `You are a helpful, knowledgeable, and friendly AI assistant. You can:

1. Answer questions on a wide variety of topics
2. Help with problem-solving and decision-making
3. Provide explanations and clarifications
4. Engage in casual conversation
5. Offer creative suggestions and ideas

Key behaviors:
- Be conversational and personable
- Ask clarifying questions when needed
- Provide accurate and helpful information
- Admit when you don't know something
- Maintain context from previous messages in the conversation
- Use tools when appropriate to enhance your responses

Remember to be helpful, harmless, and honest in all interactions.`,

  model: openai('gpt-4o'),

  // Enable memory for context retention
  memory: new Memory({
    provider: 'postgres',
    config: {
      connectionString: process.env.POSTGRES_URL,
    },
  }),

  // Dynamic configuration based on user context
  dynamicConfig: {
    instructions: (context) => {
      const userId = context.get('userId');
      const userPreferences = context.get('userPreferences');

      let instructions = `You are a helpful AI assistant.`;

      if (userPreferences?.language) {
        instructions += ` Respond primarily in ${userPreferences.language}.`;
      }

      if (userPreferences?.tone === 'formal') {
        instructions += ` Use a formal, professional tone.`;
      } else if (userPreferences?.tone === 'casual') {
        instructions += ` Use a casual, friendly tone.`;
      }

      return instructions;
    },

    model: (context) => {
      const userTier = context.get('userTier');

      // Premium users get access to better models
      if (userTier === 'premium') {
        return openai('gpt-4o');
      }

      return openai('gpt-4o-mini');
    },
  },

  // Tools available to this agent
  tools: ['webSearch', 'fileAnalysis', 'ragSearch', 'ragContext'],

  // Default generation options
  defaultGenerateOptions: {
    maxSteps: 3,
    temperature: 0.7,
  },
});
