'use client';

import { useArtifactSelector } from '@/hooks/use-artifact';
import { ChatSDKError } from '@/lib/errors';
import type { Attachment, ChatMessage } from '@/lib/types';
import { fetchWithErrorHandlers, generateUUID } from '@/lib/utils';
import { useChat } from '@ai-sdk/react';
import { DefaultChatTransport } from 'ai';
import { Brain, Zap } from 'lucide-react';
import type { Session } from 'next-auth';
import { useEffect, useState } from 'react';
import { AgentSelector } from './agent-selector';
import { Artifact } from './artifact';
import { ChatHeader } from './chat-header';
import { useDataStream } from './data-stream-provider';
import { MemoryPanel } from './memory-panel';
import { Messages } from './messages';
import { MultimodalInput } from './multimodal-input';
import { toast } from './toast';
import { Badge } from './ui/badge';
import type { VisibilityType } from './visibility-selector';
import { WorkflowPanel } from './workflow-panel';

interface EnhancedChatProps {
  id: string;
  initialMessages: ChatMessage[];
  initialChatModel: string;
  initialVisibilityType: VisibilityType;
  isReadonly: boolean;
  session: Session;
  autoResume: boolean;
}

export function EnhancedChat({
  id,
  initialMessages,
  initialChatModel,
  initialVisibilityType,
  isReadonly,
  session,
  autoResume,
}: EnhancedChatProps) {
  const [selectedAgent, setSelectedAgent] = useState('generalChat');
  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const [agentStatus, setAgentStatus] = useState<
    'idle' | 'thinking' | 'using-tools'
  >('idle');
  const [currentTool, setCurrentTool] = useState<string | null>(null);
  const { setDataStream } = useDataStream();

  const {
    messages,
    setMessages,
    input,
    setInput,
    sendMessage,
    status,
    stop,
    regenerate,
  } = useChat<ChatMessage>({
    id,
    messages: initialMessages,
    experimental_throttle: 100,
    generateId: generateUUID,
    transport: new DefaultChatTransport({
      api: '/api/mastra-chat',
      fetch: fetchWithErrorHandlers,
      prepareSendMessagesRequest({ messages, id, body }) {
        return {
          body: {
            id,
            message: messages.at(-1),
            selectedAgent,
            selectedChatModel: initialChatModel,
            selectedVisibilityType: initialVisibilityType,
            userPreferences: {
              language: 'en',
              tone: 'casual',
            },
            projectContext:
              selectedAgent === 'codeAssistant'
                ? {
                    type: 'web',
                    language: 'typescript',
                  }
                : undefined,
            ...body,
          },
        };
      },
    }),
    onData: (dataPart) => {
      setDataStream((ds) => (ds ? [...ds, dataPart] : []));

      // Handle agent status updates
      if (dataPart.type === 'tool-call') {
        setAgentStatus('using-tools');
        setCurrentTool(dataPart.toolName);
      } else if (dataPart.type === 'tool-result') {
        setCurrentTool(null);
      }
    },
    onFinish: () => {
      setAgentStatus('idle');
      setCurrentTool(null);
    },
    onError: (error) => {
      setAgentStatus('idle');
      setCurrentTool(null);

      if (error instanceof ChatSDKError) {
        toast({
          type: 'error',
          description: error.message,
        });
      }
    },
  });

  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  // Update agent status based on chat status
  useEffect(() => {
    if (status === 'streaming') {
      setAgentStatus('thinking');
    } else if (status === 'ready') {
      setAgentStatus('idle');
    }
  }, [status]);

  const handleAgentChange = (agentId: string) => {
    setSelectedAgent(agentId);

    // Show a toast to inform user about agent change
    const agentNames = {
      generalChat: 'General Assistant',
      codeAssistant: 'Code Assistant',
      research: 'Research Assistant',
      documentAnalysis: 'Document Analyst',
    };

    toast({
      type: 'success',
      description: `Switched to ${agentNames[agentId as keyof typeof agentNames]}`,
    });
  };

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        {/* Enhanced Chat Header with Agent Selector */}
        <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-4">
              <ChatHeader
                chatId={id}
                selectedModelId={initialChatModel}
                selectedVisibilityType={initialVisibilityType}
                isReadonly={isReadonly}
                session={session}
              />
            </div>

            <div className="flex items-center gap-4">
              {/* Agent Status Indicator */}
              {agentStatus !== 'idle' && (
                <div className="flex items-center gap-2">
                  {agentStatus === 'thinking' && (
                    <Badge variant="secondary" className="animate-pulse">
                      <Brain className="w-3 h-3 mr-1" />
                      Thinking...
                    </Badge>
                  )}
                  {agentStatus === 'using-tools' && currentTool && (
                    <Badge variant="secondary" className="animate-pulse">
                      <Zap className="w-3 h-3 mr-1" />
                      Using {currentTool}
                    </Badge>
                  )}
                </div>
              )}

              {/* Memory Panel */}
              <MemoryPanel
                chatId={id}
                userId={session.user.id}
                agentId={selectedAgent}
              />

              {/* Workflow Panel */}
              <WorkflowPanel
                chatId={id}
                onWorkflowResult={(result) => {
                  // Handle workflow results - could add to chat or show in artifact
                  console.log('Workflow completed:', result);
                }}
              />

              {/* RAG Panel */}
              <RAGPanel chatId={id} userId={session.user.id} />

              {/* Agent Selector */}
              <AgentSelector
                selectedAgent={selectedAgent}
                onAgentChange={handleAgentChange}
                userTier={session.user?.tier || 'free'}
              />
            </div>
          </div>
        </div>

        {/* Messages */}
        <Messages
          chatId={id}
          status={status}
          votes={[]} // You can integrate with your existing voting system
          messages={messages}
          setMessages={setMessages}
          regenerate={regenerate}
          isReadonly={isReadonly}
          isArtifactVisible={isArtifactVisible}
        />

        {/* Input */}
        <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
          {!isReadonly && (
            <MultimodalInput
              chatId={id}
              input={input}
              setInput={setInput}
              status={status}
              stop={stop}
              attachments={attachments}
              setAttachments={setAttachments}
              messages={messages}
              setMessages={setMessages}
              sendMessage={sendMessage}
              selectedVisibilityType={initialVisibilityType}
            />
          )}
        </form>
      </div>

      {/* Artifact Panel */}
      <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        sendMessage={sendMessage}
        messages={messages}
        setMessages={setMessages}
        regenerate={regenerate}
        votes={[]}
        isReadonly={isReadonly}
        selectedVisibilityType={initialVisibilityType}
      />
    </>
  );
}
