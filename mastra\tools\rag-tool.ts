import { createTool } from '@mastra/core';
import { z } from 'zod';
import { chromaRAGService } from '@/lib/rag/chroma-service';

export const ragSearchTool = createTool({
  id: 'ragSearch',
  name: 'RAG Search',
  description: 'Search through conversation history and knowledge base using semantic similarity to find relevant context and information.',
  
  inputSchema: z.object({
    query: z.string().describe('The search query to find relevant information'),
    limit: z.number().optional().default(5).describe('Maximum number of results to return (1-20)'),
    includeTypes: z.array(z.enum(['message', 'context', 'knowledge'])).optional().describe('Types of documents to include in search'),
    minRelevanceScore: z.number().optional().default(0.3).describe('Minimum relevance score (0-1)'),
    timeRange: z.object({
      start: z.number().optional().describe('Start timestamp for time-based filtering'),
      end: z.number().optional().describe('End timestamp for time-based filtering'),
    }).optional().describe('Time range for filtering results'),
  }),

  outputSchema: z.object({
    results: z.array(z.object({
      id: z.string(),
      content: z.string(),
      relevanceScore: z.number(),
      metadata: z.object({
        userId: z.string(),
        chatId: z.string(),
        messageId: z.string().optional(),
        timestamp: z.number(),
        type: z.enum(['message', 'context', 'knowledge']),
        source: z.string().optional(),
        title: z.string().optional(),
      }),
    })),
    totalResults: z.number(),
    searchQuery: z.string(),
  }),

  execute: async ({ query, limit = 5, includeTypes, minRelevanceScore = 0.3, timeRange }, { runtimeContext }) => {
    try {
      const userId = runtimeContext.get('userId');
      const chatId = runtimeContext.get('chatId');

      if (!userId || !chatId) {
        throw new Error('User ID and Chat ID are required for RAG search');
      }

      const results = await chromaRAGService.searchDocuments(
        query,
        userId,
        chatId,
        {
          limit: Math.min(Math.max(limit, 1), 20),
          minRelevanceScore: Math.max(0, Math.min(minRelevanceScore, 1)),
          includeTypes,
          timeRange,
        }
      );

      return {
        results: results.map(result => ({
          id: result.id,
          content: result.content,
          relevanceScore: Math.round(result.relevanceScore * 100) / 100,
          metadata: result.metadata,
        })),
        totalResults: results.length,
        searchQuery: query,
      };
    } catch (error) {
      console.error('RAG search error:', error);
      throw new Error(`RAG search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

export const ragAddTool = createTool({
  id: 'ragAdd',
  name: 'RAG Add Document',
  description: 'Add a document or piece of information to the knowledge base for future retrieval.',
  
  inputSchema: z.object({
    content: z.string().describe('The content to add to the knowledge base'),
    type: z.enum(['message', 'context', 'knowledge']).describe('Type of document being added'),
    title: z.string().optional().describe('Optional title for the document'),
    source: z.string().optional().describe('Source of the information'),
    messageId: z.string().optional().describe('Associated message ID if applicable'),
  }),

  outputSchema: z.object({
    documentId: z.string(),
    success: z.boolean(),
    message: z.string(),
  }),

  execute: async ({ content, type, title, source, messageId }, { runtimeContext }) => {
    try {
      const userId = runtimeContext.get('userId');
      const chatId = runtimeContext.get('chatId');

      if (!userId || !chatId) {
        throw new Error('User ID and Chat ID are required for adding documents');
      }

      const documentId = await chromaRAGService.addDocument({
        content,
        metadata: {
          userId,
          chatId,
          messageId,
          timestamp: Date.now(),
          type,
          source,
          title,
        },
      });

      return {
        documentId,
        success: true,
        message: `Document added successfully with ID: ${documentId}`,
      };
    } catch (error) {
      console.error('RAG add document error:', error);
      return {
        documentId: '',
        success: false,
        message: `Failed to add document: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  },
});

export const ragContextTool = createTool({
  id: 'ragContext',
  name: 'RAG Get Context',
  description: 'Get recent conversation context and relevant background information for the current chat session.',
  
  inputSchema: z.object({
    limit: z.number().optional().default(10).describe('Maximum number of context items to return'),
    includeRecentMessages: z.boolean().optional().default(true).describe('Include recent messages in context'),
  }),

  outputSchema: z.object({
    context: z.array(z.object({
      id: z.string(),
      content: z.string(),
      timestamp: z.number(),
      type: z.enum(['message', 'context', 'knowledge']),
      relevanceScore: z.number(),
    })),
    totalItems: z.number(),
    chatStats: z.object({
      documentCount: z.number(),
      collectionName: z.string(),
    }),
  }),

  execute: async ({ limit = 10, includeRecentMessages = true }, { runtimeContext }) => {
    try {
      const userId = runtimeContext.get('userId');
      const chatId = runtimeContext.get('chatId');

      if (!userId || !chatId) {
        throw new Error('User ID and Chat ID are required for getting context');
      }

      let contextItems = [];

      if (includeRecentMessages) {
        // Get recent conversation context
        const conversationContext = await chromaRAGService.getConversationContext(
          userId,
          chatId,
          Math.ceil(limit * 0.7) // 70% recent messages
        );
        contextItems.push(...conversationContext);
      }

      // Get collection statistics
      const stats = await chromaRAGService.getCollectionStats(userId, chatId);

      return {
        context: contextItems.slice(0, limit).map(item => ({
          id: item.id,
          content: item.content,
          timestamp: item.metadata.timestamp,
          type: item.metadata.type,
          relevanceScore: item.relevanceScore,
        })),
        totalItems: contextItems.length,
        chatStats: {
          documentCount: stats.documentCount,
          collectionName: stats.collectionName,
        },
      };
    } catch (error) {
      console.error('RAG get context error:', error);
      throw new Error(`Failed to get context: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  },
});

export const ragHealthTool = createTool({
  id: 'ragHealth',
  name: 'RAG Health Check',
  description: 'Check the health and status of the RAG system and ChromaDB connection.',
  
  inputSchema: z.object({}),

  outputSchema: z.object({
    healthy: z.boolean(),
    status: z.string(),
    timestamp: z.number(),
  }),

  execute: async (_, { runtimeContext }) => {
    try {
      const healthy = await chromaRAGService.healthCheck();
      
      return {
        healthy,
        status: healthy ? 'ChromaDB connection is healthy' : 'ChromaDB connection failed',
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error('RAG health check error:', error);
      return {
        healthy: false,
        status: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: Date.now(),
      };
    }
  },
});
