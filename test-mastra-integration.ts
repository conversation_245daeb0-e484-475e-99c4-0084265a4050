/**
 * Test script to verify Mastra integration
 * Run with: npx tsx test-mastra-integration.ts
 */

// Simple test without database connection
console.log('🚀 Testing Mastra Integration (Basic)...\n');

async function testMastraIntegration() {
  console.log('🚀 Testing Mastra Integration...\n');

  try {
    // Test 1: Verify agents are loaded
    console.log('1. Testing Agent Loading...');
    const agents = mastra.getAgents();
    console.log(`✅ Loaded ${Object.keys(agents).length} agents:`);
    Object.keys(agents).forEach(agentId => {
      console.log(`   - ${agentId}: ${agents[agentId].name}`);
    });
    console.log();

    // Test 2: Verify tools are loaded
    console.log('2. Testing Tool Loading...');
    const tools = mastra.getTools();
    console.log(`✅ Loaded ${tools.length} tools:`);
    tools.forEach(tool => {
      console.log(`   - ${tool.id}: ${tool.name}`);
    });
    console.log();

    // Test 3: Test a simple agent interaction
    console.log('3. Testing Agent Interaction...');
    const generalAgent = mastra.getAgent('generalChat');
    if (generalAgent) {
      const response = await generalAgent.generate([
        {
          role: 'user',
          content: 'Hello! Can you tell me what you can help with?',
        },
      ]);
      console.log('✅ Agent Response:', response.text.substring(0, 100) + '...');
    } else {
      console.log('❌ General chat agent not found');
    }
    console.log();

    // Test 4: Test tool execution
    console.log('4. Testing Tool Execution...');
    const fileAnalysisTool = tools.find(t => t.id === 'fileAnalysis');
    if (fileAnalysisTool) {
      try {
        // Test with package.json
        const result = await fileAnalysisTool.execute({
          filePath: './package.json',
          analysisType: 'metadata',
        }, { runtimeContext: new (await import('@mastra/core')).RuntimeContext() });
        console.log('✅ File Analysis Tool Result:', {
          filename: result.filename,
          fileSize: result.fileSize,
          fileType: result.fileType,
        });
      } catch (error) {
        console.log('⚠️  File Analysis Tool Error:', error.message);
      }
    } else {
      console.log('❌ File analysis tool not found');
    }
    console.log();

    // Test 5: Test workflows
    console.log('5. Testing Workflow Loading...');
    const workflows = mastra.getWorkflows();
    console.log(`✅ Loaded ${workflows.length} workflows:`);
    workflows.forEach(workflow => {
      console.log(`   - ${workflow.name}: ${workflow.description}`);
    });
    console.log();

    // Test 6: Test memory configuration
    console.log('6. Testing Memory Configuration...');
    const memory = mastra.getMemory();
    if (memory) {
      console.log('✅ Memory system configured');
    } else {
      console.log('⚠️  Memory system not configured');
    }
    console.log();

    console.log('🎉 Mastra Integration Test Complete!\n');
    console.log('Next steps:');
    console.log('1. Start your development server: npm run dev');
    console.log('2. Navigate to a chat page');
    console.log('3. Try selecting different agents');
    console.log('4. Test the memory and workflow panels');
    console.log('5. Execute a workflow for comprehensive testing');

  } catch (error) {
    console.error('❌ Integration test failed:', error);
    console.log('\nTroubleshooting:');
    console.log('1. Make sure all dependencies are installed: pnpm install');
    console.log('2. Check your environment variables');
    console.log('3. Verify your database connection');
    console.log('4. Review the MASTRA_INTEGRATION.md file for setup instructions');
  }
}

// Run the test
testMastraIntegration();
