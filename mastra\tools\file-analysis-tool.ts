import { createTool } from '@mastra/core';
import { z } from 'zod';
import { readFile, stat } from 'fs/promises';
import { extname, basename } from 'path';

export const fileAnalysisTool = createTool({
  id: 'fileAnalysis',
  name: 'File Analysis',
  description: 'Analyze files to extract content, metadata, and insights. Supports text files, code files, and basic binary file analysis.',
  
  inputSchema: z.object({
    filePath: z.string().describe('Path to the file to analyze'),
    analysisType: z.enum(['content', 'metadata', 'structure', 'summary']).optional().default('content').describe('Type of analysis to perform'),
    maxSize: z.number().optional().default(1024 * 1024).describe('Maximum file size to analyze in bytes (default 1MB)'),
  }),

  outputSchema: z.object({
    filename: z.string(),
    fileSize: z.number(),
    fileType: z.string(),
    extension: z.string(),
    content: z.string().optional(),
    metadata: z.object({
      lines: z.number().optional(),
      words: z.number().optional(),
      characters: z.number().optional(),
      encoding: z.string().optional(),
    }).optional(),
    structure: z.object({
      sections: z.array(z.string()).optional(),
      functions: z.array(z.string()).optional(),
      classes: z.array(z.string()).optional(),
      imports: z.array(z.string()).optional(),
    }).optional(),
    summary: z.string().optional(),
    error: z.string().optional(),
  }),

  execute: async ({ filePath, analysisType = 'content', maxSize = 1024 * 1024 }, { runtimeContext }) => {
    try {
      // Get file stats
      const stats = await stat(filePath);
      const filename = basename(filePath);
      const extension = extname(filePath).toLowerCase();
      
      // Check file size
      if (stats.size > maxSize) {
        throw new Error(`File size (${stats.size} bytes) exceeds maximum allowed size (${maxSize} bytes)`);
      }

      const result = {
        filename,
        fileSize: stats.size,
        fileType: getFileType(extension),
        extension,
      };

      // Determine if file is text-based
      const isTextFile = isTextBasedFile(extension);
      
      if (!isTextFile && analysisType === 'content') {
        return {
          ...result,
          error: 'Cannot analyze content of binary files',
        };
      }

      // Read file content for text files
      let content = '';
      if (isTextFile) {
        try {
          content = await readFile(filePath, 'utf-8');
        } catch (error) {
          // Try reading as binary and convert to string
          const buffer = await readFile(filePath);
          content = buffer.toString('utf-8');
        }
      }

      // Perform requested analysis
      switch (analysisType) {
        case 'content':
          return {
            ...result,
            content: content.substring(0, 10000), // Limit content size
          };

        case 'metadata':
          return {
            ...result,
            metadata: analyzeMetadata(content),
          };

        case 'structure':
          return {
            ...result,
            structure: analyzeStructure(content, extension),
          };

        case 'summary':
          return {
            ...result,
            summary: generateSummary(content, extension, filename),
          };

        default:
          return {
            ...result,
            content: content.substring(0, 10000),
          };
      }

    } catch (error: any) {
      return {
        filename: basename(filePath),
        fileSize: 0,
        fileType: 'unknown',
        extension: extname(filePath),
        error: error.message,
      };
    }
  },
});

function getFileType(extension: string): string {
  const typeMap: Record<string, string> = {
    '.js': 'JavaScript',
    '.ts': 'TypeScript',
    '.jsx': 'React JSX',
    '.tsx': 'React TSX',
    '.py': 'Python',
    '.java': 'Java',
    '.cpp': 'C++',
    '.c': 'C',
    '.cs': 'C#',
    '.go': 'Go',
    '.rs': 'Rust',
    '.php': 'PHP',
    '.rb': 'Ruby',
    '.swift': 'Swift',
    '.kt': 'Kotlin',
    '.html': 'HTML',
    '.css': 'CSS',
    '.scss': 'SCSS',
    '.less': 'LESS',
    '.json': 'JSON',
    '.xml': 'XML',
    '.yaml': 'YAML',
    '.yml': 'YAML',
    '.md': 'Markdown',
    '.txt': 'Text',
    '.csv': 'CSV',
    '.sql': 'SQL',
    '.sh': 'Shell Script',
    '.bat': 'Batch File',
    '.ps1': 'PowerShell',
  };
  
  return typeMap[extension] || 'Unknown';
}

function isTextBasedFile(extension: string): boolean {
  const textExtensions = [
    '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.cs',
    '.go', '.rs', '.php', '.rb', '.swift', '.kt', '.html', '.css',
    '.scss', '.less', '.json', '.xml', '.yaml', '.yml', '.md', '.txt',
    '.csv', '.sql', '.sh', '.bat', '.ps1', '.gitignore', '.env',
  ];
  
  return textExtensions.includes(extension) || extension === '';
}

function analyzeMetadata(content: string) {
  const lines = content.split('\n').length;
  const words = content.split(/\s+/).filter(word => word.length > 0).length;
  const characters = content.length;
  
  return {
    lines,
    words,
    characters,
    encoding: 'utf-8',
  };
}

function analyzeStructure(content: string, extension: string) {
  const structure: any = {};
  
  if (['.js', '.ts', '.jsx', '.tsx'].includes(extension)) {
    // Analyze JavaScript/TypeScript structure
    structure.functions = extractFunctions(content);
    structure.classes = extractClasses(content);
    structure.imports = extractImports(content);
  } else if (extension === '.py') {
    // Analyze Python structure
    structure.functions = extractPythonFunctions(content);
    structure.classes = extractPythonClasses(content);
    structure.imports = extractPythonImports(content);
  }
  
  return structure;
}

function extractFunctions(content: string): string[] {
  const functionRegex = /(?:function\s+(\w+)|const\s+(\w+)\s*=\s*(?:async\s+)?\(|(\w+)\s*:\s*(?:async\s+)?\()/g;
  const functions: string[] = [];
  let match;
  
  while ((match = functionRegex.exec(content)) !== null) {
    const functionName = match[1] || match[2] || match[3];
    if (functionName) {
      functions.push(functionName);
    }
  }
  
  return functions;
}

function extractClasses(content: string): string[] {
  const classRegex = /class\s+(\w+)/g;
  const classes: string[] = [];
  let match;
  
  while ((match = classRegex.exec(content)) !== null) {
    classes.push(match[1]);
  }
  
  return classes;
}

function extractImports(content: string): string[] {
  const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
  const imports: string[] = [];
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    imports.push(match[1]);
  }
  
  return imports;
}

function extractPythonFunctions(content: string): string[] {
  const functionRegex = /def\s+(\w+)\s*\(/g;
  const functions: string[] = [];
  let match;
  
  while ((match = functionRegex.exec(content)) !== null) {
    functions.push(match[1]);
  }
  
  return functions;
}

function extractPythonClasses(content: string): string[] {
  const classRegex = /class\s+(\w+)/g;
  const classes: string[] = [];
  let match;
  
  while ((match = classRegex.exec(content)) !== null) {
    classes.push(match[1]);
  }
  
  return classes;
}

function extractPythonImports(content: string): string[] {
  const importRegex = /(?:from\s+(\S+)\s+import|import\s+(\S+))/g;
  const imports: string[] = [];
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    imports.push(match[1] || match[2]);
  }
  
  return imports;
}

function generateSummary(content: string, extension: string, filename: string): string {
  const metadata = analyzeMetadata(content);
  const fileType = getFileType(extension);
  
  let summary = `File: ${filename} (${fileType})\n`;
  summary += `Size: ${metadata.lines} lines, ${metadata.words} words, ${metadata.characters} characters\n`;
  
  if (['.js', '.ts', '.jsx', '.tsx', '.py'].includes(extension)) {
    const structure = analyzeStructure(content, extension);
    if (structure.functions?.length) {
      summary += `Functions: ${structure.functions.length} (${structure.functions.slice(0, 5).join(', ')}${structure.functions.length > 5 ? '...' : ''})\n`;
    }
    if (structure.classes?.length) {
      summary += `Classes: ${structure.classes.length} (${structure.classes.slice(0, 3).join(', ')}${structure.classes.length > 3 ? '...' : ''})\n`;
    }
    if (structure.imports?.length) {
      summary += `Imports: ${structure.imports.length} modules\n`;
    }
  }
  
  // Add content preview
  const preview = content.substring(0, 200).replace(/\n/g, ' ').trim();
  if (preview) {
    summary += `Preview: ${preview}${content.length > 200 ? '...' : ''}`;
  }
  
  return summary;
}
