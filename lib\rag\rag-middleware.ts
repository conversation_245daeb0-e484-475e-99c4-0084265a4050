import { chromaRAGService, type RAGDocument } from './chroma-service';
import type { Message } from 'ai';

export interface RAGMiddlewareOptions {
  autoStoreMessages?: boolean;
  autoStoreContext?: boolean;
  contextWindowSize?: number;
  enableSemanticSearch?: boolean;
}

export class RAGMiddleware {
  private options: Required<RAGMiddlewareOptions>;

  constructor(options: RAGMiddlewareOptions = {}) {
    this.options = {
      autoStoreMessages: true,
      autoStoreContext: true,
      contextWindowSize: 10,
      enableSemanticSearch: true,
      ...options,
    };
  }

  /**
   * Store a chat message in the RAG system
   */
  async storeMessage(
    message: Message,
    userId: string,
    chatId: string,
    messageId?: string
  ): Promise<string | null> {
    if (!this.options.autoStoreMessages) return null;

    try {
      const documentId = await chromaRAGService.addDocument({
        content: message.content,
        metadata: {
          userId,
          chatId,
          messageId: messageId || message.id,
          timestamp: Date.now(),
          type: 'message',
          source: `${message.role}_message`,
          title: `${message.role} message`,
        },
      });

      return documentId;
    } catch (error) {
      console.error('Error storing message in RAG:', error);
      return null;
    }
  }

  /**
   * Store multiple messages in batch
   */
  async storeMessages(
    messages: Message[],
    userId: string,
    chatId: string
  ): Promise<string[]> {
    if (!this.options.autoStoreMessages || messages.length === 0) return [];

    try {
      const documents = messages.map(message => ({
        content: message.content,
        metadata: {
          userId,
          chatId,
          messageId: message.id,
          timestamp: Date.now(),
          type: 'message' as const,
          source: `${message.role}_message`,
          title: `${message.role} message`,
        },
      }));

      return await chromaRAGService.addDocuments(documents);
    } catch (error) {
      console.error('Error storing messages in RAG:', error);
      return [];
    }
  }

  /**
   * Get relevant context for a user query
   */
  async getRelevantContext(
    query: string,
    userId: string,
    chatId: string,
    options: {
      limit?: number;
      includeRecentMessages?: boolean;
      minRelevanceScore?: number;
    } = {}
  ): Promise<{
    relevantContext: string[];
    recentMessages: string[];
    totalContextItems: number;
  }> {
    const {
      limit = this.options.contextWindowSize,
      includeRecentMessages = true,
      minRelevanceScore = 0.3,
    } = options;

    try {
      const results = await Promise.all([
        // Get semantically relevant context
        this.options.enableSemanticSearch
          ? chromaRAGService.searchDocuments(query, userId, chatId, {
              limit: Math.ceil(limit * 0.7),
              minRelevanceScore,
              includeTypes: ['message', 'context', 'knowledge'],
            })
          : [],
        
        // Get recent conversation context
        includeRecentMessages
          ? chromaRAGService.getConversationContext(userId, chatId, Math.ceil(limit * 0.3))
          : [],
      ]);

      const [semanticResults, recentResults] = results;

      // Deduplicate results by ID
      const seenIds = new Set<string>();
      const relevantContext: string[] = [];
      const recentMessages: string[] = [];

      // Add semantic results
      for (const result of semanticResults) {
        if (!seenIds.has(result.id)) {
          seenIds.add(result.id);
          relevantContext.push(
            `[${result.metadata.type}] ${result.content} (relevance: ${Math.round(result.relevanceScore * 100)}%)`
          );
        }
      }

      // Add recent messages
      for (const result of recentResults) {
        if (!seenIds.has(result.id)) {
          seenIds.add(result.id);
          recentMessages.push(`[recent] ${result.content}`);
        }
      }

      return {
        relevantContext,
        recentMessages,
        totalContextItems: relevantContext.length + recentMessages.length,
      };
    } catch (error) {
      console.error('Error getting relevant context:', error);
      return {
        relevantContext: [],
        recentMessages: [],
        totalContextItems: 0,
      };
    }
  }

  /**
   * Store contextual information (not a direct message)
   */
  async storeContext(
    content: string,
    userId: string,
    chatId: string,
    metadata: {
      title?: string;
      source?: string;
      type?: 'context' | 'knowledge';
    } = {}
  ): Promise<string | null> {
    if (!this.options.autoStoreContext) return null;

    try {
      const documentId = await chromaRAGService.addDocument({
        content,
        metadata: {
          userId,
          chatId,
          timestamp: Date.now(),
          type: metadata.type || 'context',
          source: metadata.source || 'system',
          title: metadata.title,
        },
      });

      return documentId;
    } catch (error) {
      console.error('Error storing context in RAG:', error);
      return null;
    }
  }

  /**
   * Enhance a message with relevant context
   */
  async enhanceMessageWithContext(
    message: Message,
    userId: string,
    chatId: string
  ): Promise<{
    enhancedMessage: Message;
    contextUsed: string[];
    contextSummary: string;
  }> {
    if (message.role !== 'user') {
      return {
        enhancedMessage: message,
        contextUsed: [],
        contextSummary: '',
      };
    }

    try {
      const context = await this.getRelevantContext(message.content, userId, chatId);
      
      if (context.totalContextItems === 0) {
        return {
          enhancedMessage: message,
          contextUsed: [],
          contextSummary: 'No relevant context found.',
        };
      }

      // Create context summary
      const allContext = [...context.relevantContext, ...context.recentMessages];
      const contextSummary = `Found ${context.totalContextItems} relevant context items from conversation history and knowledge base.`;

      // Enhance the message with context
      const enhancedContent = `${message.content}

[CONTEXT FROM CONVERSATION HISTORY]
${allContext.slice(0, 5).join('\n')}
${allContext.length > 5 ? `\n... and ${allContext.length - 5} more items` : ''}`;

      const enhancedMessage: Message = {
        ...message,
        content: enhancedContent,
      };

      return {
        enhancedMessage,
        contextUsed: allContext,
        contextSummary,
      };
    } catch (error) {
      console.error('Error enhancing message with context:', error);
      return {
        enhancedMessage: message,
        contextUsed: [],
        contextSummary: 'Error retrieving context.',
      };
    }
  }

  /**
   * Clear chat session data
   */
  async clearChatSession(userId: string, chatId: string): Promise<boolean> {
    try {
      await chromaRAGService.clearChatSession(userId, chatId);
      return true;
    } catch (error) {
      console.error('Error clearing chat session:', error);
      return false;
    }
  }

  /**
   * Get chat session statistics
   */
  async getChatStats(userId: string, chatId: string) {
    try {
      return await chromaRAGService.getCollectionStats(userId, chatId);
    } catch (error) {
      console.error('Error getting chat stats:', error);
      return {
        documentCount: 0,
        collectionName: `user_${userId}_chat_${chatId}`,
        userId,
        chatId,
      };
    }
  }

  /**
   * Search across all user's chat sessions
   */
  async searchAcrossChats(
    query: string,
    userId: string,
    options: {
      limit?: number;
      minRelevanceScore?: number;
    } = {}
  ): Promise<Array<{
    chatId: string;
    results: Array<{
      content: string;
      relevanceScore: number;
      timestamp: number;
    }>;
  }>> {
    // This would require a more complex implementation
    // For now, we focus on per-chat RAG
    console.warn('Cross-chat search not implemented yet');
    return [];
  }
}

// Default RAG middleware instance
export const ragMiddleware = new RAGMiddleware({
  autoStoreMessages: true,
  autoStoreContext: true,
  contextWindowSize: 10,
  enableSemanticSearch: true,
});
