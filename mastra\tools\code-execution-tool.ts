import { createTool } from '@mastra/core';
import { z } from 'zod';
import { exec } from 'child_process';
import { promisify } from 'util';
import { writeFile, unlink, mkdir } from 'fs/promises';
import { join } from 'path';
import { tmpdir } from 'os';

const execAsync = promisify(exec);

export const codeExecutionTool = createTool({
  id: 'codeExecution',
  name: 'Code Execution',
  description: 'Execute code in various programming languages safely in a sandboxed environment. Supports JavaScript, Python, and shell commands.',
  
  inputSchema: z.object({
    code: z.string().describe('The code to execute'),
    language: z.enum(['javascript', 'python', 'bash', 'typescript']).describe('Programming language of the code'),
    timeout: z.number().optional().default(10000).describe('Execution timeout in milliseconds'),
    args: z.array(z.string()).optional().default([]).describe('Command line arguments (for applicable languages)'),
  }),

  outputSchema: z.object({
    stdout: z.string(),
    stderr: z.string(),
    exitCode: z.number(),
    executionTime: z.number(),
    error: z.string().optional(),
  }),

  execute: async ({ code, language, timeout = 10000, args = [] }, { runtimeContext }) => {
    const startTime = Date.now();
    
    try {
      // Create a temporary directory for code execution
      const tempDir = join(tmpdir(), `mastra-code-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
      await mkdir(tempDir, { recursive: true });
      
      let command: string;
      let filename: string;
      
      switch (language) {
        case 'javascript':
          filename = join(tempDir, 'script.js');
          await writeFile(filename, code);
          command = `node "${filename}" ${args.join(' ')}`;
          break;
          
        case 'typescript':
          filename = join(tempDir, 'script.ts');
          await writeFile(filename, code);
          // Requires ts-node to be installed
          command = `npx ts-node "${filename}" ${args.join(' ')}`;
          break;
          
        case 'python':
          filename = join(tempDir, 'script.py');
          await writeFile(filename, code);
          command = `python3 "${filename}" ${args.join(' ')}`;
          break;
          
        case 'bash':
          filename = join(tempDir, 'script.sh');
          await writeFile(filename, code);
          command = `bash "${filename}" ${args.join(' ')}`;
          break;
          
        default:
          throw new Error(`Unsupported language: ${language}`);
      }

      // Execute the code with timeout
      const { stdout, stderr } = await execAsync(command, {
        timeout,
        cwd: tempDir,
        env: {
          ...process.env,
          // Restrict environment for security
          PATH: process.env.PATH,
          NODE_ENV: 'sandbox',
        },
      });

      // Clean up temporary files
      try {
        await unlink(filename);
      } catch (cleanupError) {
        console.warn('Failed to clean up temporary file:', cleanupError);
      }

      const executionTime = Date.now() - startTime;

      return {
        stdout: stdout.trim(),
        stderr: stderr.trim(),
        exitCode: 0,
        executionTime,
      };

    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      
      // Handle different types of execution errors
      if (error.killed && error.signal === 'SIGTERM') {
        return {
          stdout: '',
          stderr: 'Execution timed out',
          exitCode: 124,
          executionTime,
          error: 'Execution timeout exceeded',
        };
      }

      return {
        stdout: error.stdout || '',
        stderr: error.stderr || error.message || 'Unknown execution error',
        exitCode: error.code || 1,
        executionTime,
        error: error.message,
      };
    }
  },
});

// Security note: This tool executes arbitrary code and should be used with caution.
// In production, consider:
// 1. Running in a containerized environment (Docker)
// 2. Using a dedicated code execution service
// 3. Implementing additional security restrictions
// 4. Limiting available system resources
// 5. Monitoring and logging all executions
