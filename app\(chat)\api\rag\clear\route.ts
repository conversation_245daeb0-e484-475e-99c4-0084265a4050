import { NextRequest } from 'next/server';
import { auth } from '@/app/(auth)/auth';
import { chromaRAGService } from '@/lib/rag/chroma-service';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { chatId, userId } = await request.json();

    if (!chatId || !userId) {
      return new Response('Missing required fields', { status: 400 });
    }

    // Verify user can access this chat
    if (userId !== session.user.id) {
      return new Response('Forbidden', { status: 403 });
    }

    await chromaRAGService.clearChatSession(userId, chatId);

    return Response.json({
      success: true,
      message: 'RAG data cleared successfully',
    });

  } catch (error) {
    console.error('RAG clear error:', error);
    
    return Response.json(
      {
        success: false,
        error: 'Failed to clear RAG data',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
