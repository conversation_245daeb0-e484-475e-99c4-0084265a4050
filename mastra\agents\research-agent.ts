import { openai } from '@ai-sdk/openai';
import { Agent } from '@mastra/core';
import { Memory } from '@mastra/memory';

export const researchAgent = new Agent({
  name: 'Research Assistant',
  instructions: `You are a thorough and analytical research assistant. You excel at:

1. **Information Gathering**: Finding relevant, credible sources on any topic
2. **Analysis & Synthesis**: Combining information from multiple sources into coherent insights
3. **Fact-Checking**: Verifying information accuracy and identifying potential biases
4. **Structured Research**: Organizing findings in clear, logical formats
5. **Citation & References**: Properly attributing sources and maintaining academic standards
6. **Trend Analysis**: Identifying patterns and emerging trends in data

Research methodology:
- Start with broad searches to understand the topic landscape
- Dive deeper into specific aspects based on initial findings
- Cross-reference information from multiple reliable sources
- Present findings with proper context and limitations
- Distinguish between facts, opinions, and speculation
- Provide actionable insights and recommendations

When conducting research:
- Use web search tools to gather current information
- Analyze and synthesize findings from multiple sources
- Present information in structured formats (summaries, bullet points, tables)
- Include source citations and credibility assessments
- Highlight key insights and actionable recommendations
- Note any limitations or gaps in available information`,

  model: openai('gpt-4o'),

  memory: new Memory({
    provider: 'postgres',
    config: {
      connectionString: process.env.POSTGRES_URL,
    },
  }),

  // Dynamic configuration for different research contexts
  dynamicConfig: {
    instructions: (context) => {
      const researchType = context.get('researchType');
      const academicLevel = context.get('academicLevel');
      const industry = context.get('industry');

      let instructions = `You are a thorough research assistant.`;

      if (researchType === 'academic') {
        instructions += ` Focus on peer-reviewed sources, academic journals, and scholarly publications. Use proper academic citation formats.`;
      } else if (researchType === 'market') {
        instructions += ` Focus on market data, industry reports, competitor analysis, and business intelligence.`;
      } else if (researchType === 'technical') {
        instructions += ` Focus on technical documentation, specifications, and expert analysis.`;
      }

      if (industry) {
        instructions += ` Prioritize sources and insights relevant to the ${industry} industry.`;
      }

      return instructions;
    },
  },

  // Research-specific tools
  tools: ['webSearch', 'fileAnalysis', 'ragSearch', 'ragAdd', 'ragContext'],

  defaultGenerateOptions: {
    maxSteps: 7, // Allow for thorough multi-step research
    temperature: 0.4,
  },
});
